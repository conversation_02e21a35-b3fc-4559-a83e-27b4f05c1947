<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FeatureSet;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.protobuf.FeatureSet.RepeatedFieldEncoding</code>
 */
class RepeatedFieldEncoding
{
    /**
     * Generated from protobuf enum <code>REPEATED_FIELD_ENCODING_UNKNOWN = 0;</code>
     */
    const REPEATED_FIELD_ENCODING_UNKNOWN = 0;
    /**
     * Generated from protobuf enum <code>PACKED = 1;</code>
     */
    const PACKED = 1;
    /**
     * Generated from protobuf enum <code>EXPANDED = 2;</code>
     */
    const EXPANDED = 2;

    private static $valueToName = [
        self::REPEATED_FIELD_ENCODING_UNKNOWN => 'REPEATED_FIELD_ENCODING_UNKNOWN',
        self::PACKED => 'PACKED',
        self::EXPANDED => 'EXPANDED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

