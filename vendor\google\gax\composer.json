{"name": "google/gax", "type": "library", "description": "Google API Core for PHP", "keywords": ["google"], "homepage": "https://github.com/googleapis/gax-php", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^8.0", "google/auth": "^1.45", "google/grpc-gcp": "^0.4", "grpc/grpc": "^1.13", "google/protobuf": "^v3.25.3||^4.26.1", "guzzlehttp/promises": "^2.0", "guzzlehttp/psr7": "^2.0", "google/common-protos": "^4.4", "google/longrunning": "~0.4", "ramsey/uuid": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "3.*", "phpspec/prophecy-phpunit": "^2.1", "phpstan/phpstan": "^2.0"}, "conflict": {"ext-protobuf": "<3.7.0"}, "autoload": {"psr-4": {"Google\\ApiCore\\": "src", "GPBMetadata\\ApiCore\\": "metadata/ApiCore"}}, "autoload-dev": {"psr-4": {"Google\\ApiCore\\Dev\\": "dev/src", "Google\\ApiCore\\Tests\\": "tests", "GPBMetadata\\Google\\": "metadata/Google"}}, "scripts": {"regenerate-test-protos": "dev/sh/regenerate-test-protos.sh", "test": "./vendor/bin/phpunit", "cs-lint": "vendor/bin/phpcs --standard=./ruleset.xml", "cs-fix": "vendor/bin/phpcbf --standard=./ruleset.xml"}}