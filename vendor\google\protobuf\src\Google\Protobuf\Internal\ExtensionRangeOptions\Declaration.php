<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\ExtensionRangeOptions;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\GPBWire;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\InputStream;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>google.protobuf.ExtensionRangeOptions.Declaration</code>
 */
class Declaration extends \Google\Protobuf\Internal\Message
{
    /**
     * The extension number declared within the extension range.
     *
     * Generated from protobuf field <code>optional int32 number = 1;</code>
     */
    protected $number = null;
    /**
     * The fully-qualified name of the extension field. There must be a leading
     * dot in front of the full name.
     *
     * Generated from protobuf field <code>optional string full_name = 2;</code>
     */
    protected $full_name = null;
    /**
     * The fully-qualified type name of the extension field. Unlike
     * Metadata.type, Declaration.type must have a leading dot for messages
     * and enums.
     *
     * Generated from protobuf field <code>optional string type = 3;</code>
     */
    protected $type = null;
    /**
     * If true, indicates that the number is reserved in the extension range,
     * and any extension field with the number will fail to compile. Set this
     * when a declared extension field is deleted.
     *
     * Generated from protobuf field <code>optional bool reserved = 5;</code>
     */
    protected $reserved = null;
    /**
     * If true, indicates that the extension must be defined as repeated.
     * Otherwise the extension must be defined as optional.
     *
     * Generated from protobuf field <code>optional bool repeated = 6;</code>
     */
    protected $repeated = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $number
     *           The extension number declared within the extension range.
     *     @type string $full_name
     *           The fully-qualified name of the extension field. There must be a leading
     *           dot in front of the full name.
     *     @type string $type
     *           The fully-qualified type name of the extension field. Unlike
     *           Metadata.type, Declaration.type must have a leading dot for messages
     *           and enums.
     *     @type bool $reserved
     *           If true, indicates that the number is reserved in the extension range,
     *           and any extension field with the number will fail to compile. Set this
     *           when a declared extension field is deleted.
     *     @type bool $repeated
     *           If true, indicates that the extension must be defined as repeated.
     *           Otherwise the extension must be defined as optional.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Protobuf\Internal\Descriptor::initOnce();
        parent::__construct($data);
    }

    /**
     * The extension number declared within the extension range.
     *
     * Generated from protobuf field <code>optional int32 number = 1;</code>
     * @return int
     */
    public function getNumber()
    {
        return isset($this->number) ? $this->number : 0;
    }

    public function hasNumber()
    {
        return isset($this->number);
    }

    public function clearNumber()
    {
        unset($this->number);
    }

    /**
     * The extension number declared within the extension range.
     *
     * Generated from protobuf field <code>optional int32 number = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setNumber($var)
    {
        GPBUtil::checkInt32($var);
        $this->number = $var;

        return $this;
    }

    /**
     * The fully-qualified name of the extension field. There must be a leading
     * dot in front of the full name.
     *
     * Generated from protobuf field <code>optional string full_name = 2;</code>
     * @return string
     */
    public function getFullName()
    {
        return isset($this->full_name) ? $this->full_name : '';
    }

    public function hasFullName()
    {
        return isset($this->full_name);
    }

    public function clearFullName()
    {
        unset($this->full_name);
    }

    /**
     * The fully-qualified name of the extension field. There must be a leading
     * dot in front of the full name.
     *
     * Generated from protobuf field <code>optional string full_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setFullName($var)
    {
        GPBUtil::checkString($var, True);
        $this->full_name = $var;

        return $this;
    }

    /**
     * The fully-qualified type name of the extension field. Unlike
     * Metadata.type, Declaration.type must have a leading dot for messages
     * and enums.
     *
     * Generated from protobuf field <code>optional string type = 3;</code>
     * @return string
     */
    public function getType()
    {
        return isset($this->type) ? $this->type : '';
    }

    public function hasType()
    {
        return isset($this->type);
    }

    public function clearType()
    {
        unset($this->type);
    }

    /**
     * The fully-qualified type name of the extension field. Unlike
     * Metadata.type, Declaration.type must have a leading dot for messages
     * and enums.
     *
     * Generated from protobuf field <code>optional string type = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

    /**
     * If true, indicates that the number is reserved in the extension range,
     * and any extension field with the number will fail to compile. Set this
     * when a declared extension field is deleted.
     *
     * Generated from protobuf field <code>optional bool reserved = 5;</code>
     * @return bool
     */
    public function getReserved()
    {
        return isset($this->reserved) ? $this->reserved : false;
    }

    public function hasReserved()
    {
        return isset($this->reserved);
    }

    public function clearReserved()
    {
        unset($this->reserved);
    }

    /**
     * If true, indicates that the number is reserved in the extension range,
     * and any extension field with the number will fail to compile. Set this
     * when a declared extension field is deleted.
     *
     * Generated from protobuf field <code>optional bool reserved = 5;</code>
     * @param bool $var
     * @return $this
     */
    public function setReserved($var)
    {
        GPBUtil::checkBool($var);
        $this->reserved = $var;

        return $this;
    }

    /**
     * If true, indicates that the extension must be defined as repeated.
     * Otherwise the extension must be defined as optional.
     *
     * Generated from protobuf field <code>optional bool repeated = 6;</code>
     * @return bool
     */
    public function getRepeated()
    {
        return isset($this->repeated) ? $this->repeated : false;
    }

    public function hasRepeated()
    {
        return isset($this->repeated);
    }

    public function clearRepeated()
    {
        unset($this->repeated);
    }

    /**
     * If true, indicates that the extension must be defined as repeated.
     * Otherwise the extension must be defined as optional.
     *
     * Generated from protobuf field <code>optional bool repeated = 6;</code>
     * @param bool $var
     * @return $this
     */
    public function setRepeated($var)
    {
        GPBUtil::checkBool($var);
        $this->repeated = $var;

        return $this;
    }

}

