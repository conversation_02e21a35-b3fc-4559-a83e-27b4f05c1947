<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/auth.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * OAuth scopes are a way to define data and permissions on data. For example,
 * there are scopes defined for "Read-only access to Google Calendar" and
 * "Access to Cloud Platform". Users can consent to a scope for an application,
 * giving it permission to access that data on their behalf.
 * OAuth scope specifications should be fairly coarse grained; a user will need
 * to see and understand the text description of what your scope means.
 * In most cases: use one or at most two OAuth scopes for an entire family of
 * products. If your product has multiple APIs, you should probably be sharing
 * the OAuth scope across all of those APIs.
 * When you need finer grained OAuth consent screens: talk with your product
 * management about how developers will use them in practice.
 * Please note that even though each of the canonical scopes is enough for a
 * request to be accepted and passed to the backend, a request can still fail
 * due to the backend requiring additional scopes or permissions.
 *
 * Generated from protobuf message <code>google.api.OAuthRequirements</code>
 */
class OAuthRequirements extends \Google\Protobuf\Internal\Message
{
    /**
     * The list of publicly documented OAuth scopes that are allowed access. An
     * OAuth token containing any of these scopes will be accepted.
     * Example:
     *      canonical_scopes: https://www.googleapis.com/auth/calendar,
     *                        https://www.googleapis.com/auth/calendar.read
     *
     * Generated from protobuf field <code>string canonical_scopes = 1;</code>
     */
    protected $canonical_scopes = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $canonical_scopes
     *           The list of publicly documented OAuth scopes that are allowed access. An
     *           OAuth token containing any of these scopes will be accepted.
     *           Example:
     *                canonical_scopes: https://www.googleapis.com/auth/calendar,
     *                                  https://www.googleapis.com/auth/calendar.read
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Auth::initOnce();
        parent::__construct($data);
    }

    /**
     * The list of publicly documented OAuth scopes that are allowed access. An
     * OAuth token containing any of these scopes will be accepted.
     * Example:
     *      canonical_scopes: https://www.googleapis.com/auth/calendar,
     *                        https://www.googleapis.com/auth/calendar.read
     *
     * Generated from protobuf field <code>string canonical_scopes = 1;</code>
     * @return string
     */
    public function getCanonicalScopes()
    {
        return $this->canonical_scopes;
    }

    /**
     * The list of publicly documented OAuth scopes that are allowed access. An
     * OAuth token containing any of these scopes will be accepted.
     * Example:
     *      canonical_scopes: https://www.googleapis.com/auth/calendar,
     *                        https://www.googleapis.com/auth/calendar.read
     *
     * Generated from protobuf field <code>string canonical_scopes = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setCanonicalScopes($var)
    {
        GPBUtil::checkString($var, True);
        $this->canonical_scopes = $var;

        return $this;
    }

}

