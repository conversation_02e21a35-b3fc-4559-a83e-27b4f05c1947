// Notification Badge Handler for Q-Pusher
// This module manages the notification badge count for the app

import { onMessage } from "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js";
import { messagingPromise } from "./firebase-init.js";

// Initialize badge count from localStorage or set to 0
let badgeCount = parseInt(localStorage.getItem('q_notification_badge_count') || '0');

// Function to update the badge count
function updateBadgeCount(count) {
    badgeCount = count;
    
    // Store the count in localStorage for persistence
    localStorage.setItem('q_notification_badge_count', badgeCount.toString());
    
    // Update the badge if the browser supports it
    if ('setAppBadge' in navigator) {
        if (badgeCount > 0) {
            navigator.setAppBadge(badgeCount).catch(error => {
                console.error('Error setting app badge:', error);
            });
        } else {
            navigator.clearAppBadge().catch(error => {
                console.error('Error clearing app badge:', error);
            });
        }
    }
    
    // Dispatch a custom event that other parts of the app can listen for
    document.dispatchEvent(new CustomEvent('q_badge_updated', { 
        detail: { count: badgeCount } 
    }));
    
    return badgeCount;
}

// Function to increment the badge count
function incrementBadgeCount() {
    return updateBadgeCount(badgeCount + 1);
}

// Function to decrement the badge count
function decrementBadgeCount() {
    if (badgeCount > 0) {
        return updateBadgeCount(badgeCount - 1);
    }
    return badgeCount;
}

// Function to clear the badge count
function clearBadgeCount() {
    return updateBadgeCount(0);
}

// Function to get the current badge count
function getBadgeCount() {
    return badgeCount;
}

// Initialize badge handling when the DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Wait for Firebase Messaging to be initialized
        const messaging = await messagingPromise;
        
        // Listen for foreground messages to update badge
        onMessage(messaging, (payload) => {
            console.log('Badge handler received message:', payload);
            
            // Increment badge count when a new notification is received
            incrementBadgeCount();
        });
        
        // Clear badge when user interacts with the page
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                clearBadgeCount();
            }
        });
        
        // Listen for notification clicks to clear badge
        navigator.serviceWorker.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'NOTIFICATION_CLICKED') {
                clearBadgeCount();
            }
        });
        
    } catch (error) {
        console.error('Failed to initialize badge handler:', error);
    }
});

// Export the badge functions for use in other modules
export {
    updateBadgeCount,
    incrementBadgeCount,
    decrementBadgeCount,
    clearBadgeCount,
    getBadgeCount
};
