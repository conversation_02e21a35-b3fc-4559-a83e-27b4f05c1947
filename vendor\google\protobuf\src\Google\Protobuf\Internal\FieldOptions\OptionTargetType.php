<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FieldOptions;

use UnexpectedValueException;

/**
 * This indicates the types of entities that the field may apply to when used
 * as an option. If it is unset, then the field may be freely used as an
 * option on any kind of entity.
 *
 * Protobuf type <code>google.protobuf.FieldOptions.OptionTargetType</code>
 */
class OptionTargetType
{
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_UNKNOWN = 0;</code>
     */
    const TARGET_TYPE_UNKNOWN = 0;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_FILE = 1;</code>
     */
    const TARGET_TYPE_FILE = 1;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_EXTENSION_RANGE = 2;</code>
     */
    const TARGET_TYPE_EXTENSION_RANGE = 2;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_MESSAGE = 3;</code>
     */
    const TARGET_TYPE_MESSAGE = 3;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_FIELD = 4;</code>
     */
    const TARGET_TYPE_FIELD = 4;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_ONEOF = 5;</code>
     */
    const TARGET_TYPE_ONEOF = 5;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_ENUM = 6;</code>
     */
    const TARGET_TYPE_ENUM = 6;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_ENUM_ENTRY = 7;</code>
     */
    const TARGET_TYPE_ENUM_ENTRY = 7;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_SERVICE = 8;</code>
     */
    const TARGET_TYPE_SERVICE = 8;
    /**
     * Generated from protobuf enum <code>TARGET_TYPE_METHOD = 9;</code>
     */
    const TARGET_TYPE_METHOD = 9;

    private static $valueToName = [
        self::TARGET_TYPE_UNKNOWN => 'TARGET_TYPE_UNKNOWN',
        self::TARGET_TYPE_FILE => 'TARGET_TYPE_FILE',
        self::TARGET_TYPE_EXTENSION_RANGE => 'TARGET_TYPE_EXTENSION_RANGE',
        self::TARGET_TYPE_MESSAGE => 'TARGET_TYPE_MESSAGE',
        self::TARGET_TYPE_FIELD => 'TARGET_TYPE_FIELD',
        self::TARGET_TYPE_ONEOF => 'TARGET_TYPE_ONEOF',
        self::TARGET_TYPE_ENUM => 'TARGET_TYPE_ENUM',
        self::TARGET_TYPE_ENUM_ENTRY => 'TARGET_TYPE_ENUM_ENTRY',
        self::TARGET_TYPE_SERVICE => 'TARGET_TYPE_SERVICE',
        self::TARGET_TYPE_METHOD => 'TARGET_TYPE_METHOD',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

