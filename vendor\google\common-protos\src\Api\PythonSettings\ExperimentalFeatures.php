<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/client.proto

namespace Google\Api\PythonSettings;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Experimental features to be included during client library generation.
 * These fields will be deprecated once the feature graduates and is enabled
 * by default.
 *
 * Generated from protobuf message <code>google.api.PythonSettings.ExperimentalFeatures</code>
 */
class ExperimentalFeatures extends \Google\Protobuf\Internal\Message
{
    /**
     * Enables generation of asynchronous REST clients if `rest` transport is
     * enabled. By default, asynchronous REST clients will not be generated.
     * This feature will be enabled by default 1 month after launching the
     * feature in preview packages.
     *
     * Generated from protobuf field <code>bool rest_async_io_enabled = 1;</code>
     */
    protected $rest_async_io_enabled = false;
    /**
     * Enables generation of protobuf code using new types that are more
     * Pythonic which are included in `protobuf>=5.29.x`. This feature will be
     * enabled by default 1 month after launching the feature in preview
     * packages.
     *
     * Generated from protobuf field <code>bool protobuf_pythonic_types_enabled = 2;</code>
     */
    protected $protobuf_pythonic_types_enabled = false;
    /**
     * Disables generation of an unversioned Python package for this client
     * library. This means that the module names will need to be versioned in
     * import statements. For example `import google.cloud.library_v2` instead
     * of `import google.cloud.library`.
     *
     * Generated from protobuf field <code>bool unversioned_package_disabled = 3;</code>
     */
    protected $unversioned_package_disabled = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type bool $rest_async_io_enabled
     *           Enables generation of asynchronous REST clients if `rest` transport is
     *           enabled. By default, asynchronous REST clients will not be generated.
     *           This feature will be enabled by default 1 month after launching the
     *           feature in preview packages.
     *     @type bool $protobuf_pythonic_types_enabled
     *           Enables generation of protobuf code using new types that are more
     *           Pythonic which are included in `protobuf>=5.29.x`. This feature will be
     *           enabled by default 1 month after launching the feature in preview
     *           packages.
     *     @type bool $unversioned_package_disabled
     *           Disables generation of an unversioned Python package for this client
     *           library. This means that the module names will need to be versioned in
     *           import statements. For example `import google.cloud.library_v2` instead
     *           of `import google.cloud.library`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Client::initOnce();
        parent::__construct($data);
    }

    /**
     * Enables generation of asynchronous REST clients if `rest` transport is
     * enabled. By default, asynchronous REST clients will not be generated.
     * This feature will be enabled by default 1 month after launching the
     * feature in preview packages.
     *
     * Generated from protobuf field <code>bool rest_async_io_enabled = 1;</code>
     * @return bool
     */
    public function getRestAsyncIoEnabled()
    {
        return $this->rest_async_io_enabled;
    }

    /**
     * Enables generation of asynchronous REST clients if `rest` transport is
     * enabled. By default, asynchronous REST clients will not be generated.
     * This feature will be enabled by default 1 month after launching the
     * feature in preview packages.
     *
     * Generated from protobuf field <code>bool rest_async_io_enabled = 1;</code>
     * @param bool $var
     * @return $this
     */
    public function setRestAsyncIoEnabled($var)
    {
        GPBUtil::checkBool($var);
        $this->rest_async_io_enabled = $var;

        return $this;
    }

    /**
     * Enables generation of protobuf code using new types that are more
     * Pythonic which are included in `protobuf>=5.29.x`. This feature will be
     * enabled by default 1 month after launching the feature in preview
     * packages.
     *
     * Generated from protobuf field <code>bool protobuf_pythonic_types_enabled = 2;</code>
     * @return bool
     */
    public function getProtobufPythonicTypesEnabled()
    {
        return $this->protobuf_pythonic_types_enabled;
    }

    /**
     * Enables generation of protobuf code using new types that are more
     * Pythonic which are included in `protobuf>=5.29.x`. This feature will be
     * enabled by default 1 month after launching the feature in preview
     * packages.
     *
     * Generated from protobuf field <code>bool protobuf_pythonic_types_enabled = 2;</code>
     * @param bool $var
     * @return $this
     */
    public function setProtobufPythonicTypesEnabled($var)
    {
        GPBUtil::checkBool($var);
        $this->protobuf_pythonic_types_enabled = $var;

        return $this;
    }

    /**
     * Disables generation of an unversioned Python package for this client
     * library. This means that the module names will need to be versioned in
     * import statements. For example `import google.cloud.library_v2` instead
     * of `import google.cloud.library`.
     *
     * Generated from protobuf field <code>bool unversioned_package_disabled = 3;</code>
     * @return bool
     */
    public function getUnversionedPackageDisabled()
    {
        return $this->unversioned_package_disabled;
    }

    /**
     * Disables generation of an unversioned Python package for this client
     * library. This means that the module names will need to be versioned in
     * import statements. For example `import google.cloud.library_v2` instead
     * of `import google.cloud.library`.
     *
     * Generated from protobuf field <code>bool unversioned_package_disabled = 3;</code>
     * @param bool $var
     * @return $this
     */
    public function setUnversionedPackageDisabled($var)
    {
        GPBUtil::checkBool($var);
        $this->unversioned_package_disabled = $var;

        return $this;
    }

}


