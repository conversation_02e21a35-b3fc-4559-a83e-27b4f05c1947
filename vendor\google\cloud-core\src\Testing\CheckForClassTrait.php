<?php
/**
 * Copyright 2018 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace Google\Cloud\Core\Testing;

/**
 * Trait CheckForClassTrait
 *
 * @experimental
 * @internal
 */
trait CheckForClassTrait
{
    /**
     * Check whether all required classes are available, otherwise skip the tests.
     *
     * @param array $requiredClasses List of classes that must be available.
     *
     * @experimental
     * @internal
     */
    protected function checkAndSkipTest(array $requiredClasses)
    {
        foreach ($requiredClasses as $class) {
            if (!class_exists($class)) {
                $this->markTestSkipped("Missing required class: $class");
                return;
            }
        }
    }
}
