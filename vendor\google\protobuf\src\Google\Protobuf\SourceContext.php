<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/source_context.proto

namespace Google\Protobuf;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * `SourceContext` represents information about the source of a
 * protobuf element, like the file in which it is defined.
 *
 * Generated from protobuf message <code>google.protobuf.SourceContext</code>
 */
class SourceContext extends \Google\Protobuf\Internal\Message
{
    /**
     * The path-qualified name of the .proto file that contained the associated
     * protobuf element.  For example: `"google/protobuf/source_context.proto"`.
     *
     * Generated from protobuf field <code>string file_name = 1;</code>
     */
    protected $file_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $file_name
     *           The path-qualified name of the .proto file that contained the associated
     *           protobuf element.  For example: `"google/protobuf/source_context.proto"`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Protobuf\SourceContext::initOnce();
        parent::__construct($data);
    }

    /**
     * The path-qualified name of the .proto file that contained the associated
     * protobuf element.  For example: `"google/protobuf/source_context.proto"`.
     *
     * Generated from protobuf field <code>string file_name = 1;</code>
     * @return string
     */
    public function getFileName()
    {
        return $this->file_name;
    }

    /**
     * The path-qualified name of the .proto file that contained the associated
     * protobuf element.  For example: `"google/protobuf/source_context.proto"`.
     *
     * Generated from protobuf field <code>string file_name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFileName($var)
    {
        GPBUtil::checkString($var, True);
        $this->file_name = $var;

        return $this;
    }

}

