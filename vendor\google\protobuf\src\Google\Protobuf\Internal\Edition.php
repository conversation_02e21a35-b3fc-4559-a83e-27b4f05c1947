<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal;

use UnexpectedValueException;

/**
 * The full set of known editions.
 *
 * Protobuf type <code>google.protobuf.Edition</code>
 */
class Edition
{
    /**
     * A placeholder for an unknown edition value.
     *
     * Generated from protobuf enum <code>EDITION_UNKNOWN = 0;</code>
     */
    const EDITION_UNKNOWN = 0;
    /**
     * A placeholder edition for specifying default behaviors *before* a feature
     * was first introduced.  This is effectively an "infinite past".
     *
     * Generated from protobuf enum <code>EDITION_LEGACY = 900;</code>
     */
    const EDITION_LEGACY = 900;
    /**
     * Legacy syntax "editions".  These pre-date editions, but behave much like
     * distinct editions.  These can't be used to specify the edition of proto
     * files, but feature definitions must supply proto2/proto3 defaults for
     * backwards compatibility.
     *
     * Generated from protobuf enum <code>EDITION_PROTO2 = 998;</code>
     */
    const EDITION_PROTO2 = 998;
    /**
     * Generated from protobuf enum <code>EDITION_PROTO3 = 999;</code>
     */
    const EDITION_PROTO3 = 999;
    /**
     * Editions that have been released.  The specific values are arbitrary and
     * should not be depended on, but they will always be time-ordered for easy
     * comparison.
     *
     * Generated from protobuf enum <code>EDITION_2023 = 1000;</code>
     */
    const EDITION_2023 = 1000;
    /**
     * Generated from protobuf enum <code>EDITION_2024 = 1001;</code>
     */
    const EDITION_2024 = 1001;
    /**
     * Placeholder editions for testing feature resolution.  These should not be
     * used or relied on outside of tests.
     *
     * Generated from protobuf enum <code>EDITION_1_TEST_ONLY = 1;</code>
     */
    const EDITION_1_TEST_ONLY = 1;
    /**
     * Generated from protobuf enum <code>EDITION_2_TEST_ONLY = 2;</code>
     */
    const EDITION_2_TEST_ONLY = 2;
    /**
     * Generated from protobuf enum <code>EDITION_99997_TEST_ONLY = 99997;</code>
     */
    const EDITION_99997_TEST_ONLY = 99997;
    /**
     * Generated from protobuf enum <code>EDITION_99998_TEST_ONLY = 99998;</code>
     */
    const EDITION_99998_TEST_ONLY = 99998;
    /**
     * Generated from protobuf enum <code>EDITION_99999_TEST_ONLY = 99999;</code>
     */
    const EDITION_99999_TEST_ONLY = 99999;
    /**
     * Placeholder for specifying unbounded edition support.  This should only
     * ever be used by plugins that can expect to never require any changes to
     * support a new edition.
     *
     * Generated from protobuf enum <code>EDITION_MAX = 2147483647;</code>
     */
    const EDITION_MAX = 2147483647;

    private static $valueToName = [
        self::EDITION_UNKNOWN => 'EDITION_UNKNOWN',
        self::EDITION_LEGACY => 'EDITION_LEGACY',
        self::EDITION_PROTO2 => 'EDITION_PROTO2',
        self::EDITION_PROTO3 => 'EDITION_PROTO3',
        self::EDITION_2023 => 'EDITION_2023',
        self::EDITION_2024 => 'EDITION_2024',
        self::EDITION_1_TEST_ONLY => 'EDITION_1_TEST_ONLY',
        self::EDITION_2_TEST_ONLY => 'EDITION_2_TEST_ONLY',
        self::EDITION_99997_TEST_ONLY => 'EDITION_99997_TEST_ONLY',
        self::EDITION_99998_TEST_ONLY => 'EDITION_99998_TEST_ONLY',
        self::EDITION_99999_TEST_ONLY => 'EDITION_99999_TEST_ONLY',
        self::EDITION_MAX => 'EDITION_MAX',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

