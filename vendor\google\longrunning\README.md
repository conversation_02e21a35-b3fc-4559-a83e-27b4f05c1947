# Google LongRunning API for PHP

> Idiomatic PHP client for [Google LongRunning API](https://cloud.google.com/service-infrastructure/docs/service-management/reference/rpc/google.longrunning).

[![Latest Stable Version](https://poser.pugx.org/google/longrunning/v/stable)](https://packagist.org/packages/google/longrunning) [![Packagist](https://img.shields.io/packagist/dm/google/longrunning.svg)](https://packagist.org/packages/google/longrunning)

* [API documentation](https://cloud.google.com/service-infrastructure/docs/service-management/reference/rpc/google.longrunning)

**NOTE:** This repository is part of [Google Cloud PHP](https://github.com/googleapis/google-cloud-php). Any
support requests, bug reports, or development contributions should be directed to
that project.

### Installation

To begin, install the preferred dependency manager for PHP, [Composer](https://getcomposer.org/).

Now to install just this component:

```sh
$ composer require google/longrunning
```

This component supports both REST over HTTP/1.1 and gRPC. In order to take advantage of the benefits offered by gRPC (such as streaming methods)
please see our [gRPC installation guide](https://cloud.google.com/php/grpc).

### Authentication

Please see our [Authentication guide](https://github.com/googleapis/google-cloud-php/blob/main/AUTHENTICATION.md) for more information
on authenticating your client. Once authenticated, you'll be ready to start making requests.

### Debugging

Please see our [Debugging guide](https://github.com/googleapis/google-cloud-php/blob/main/DEBUG.md)
for more information about the debugging tools.

### Version

This component is considered beta. As such, it should be expected to be mostly
stable and we're working towards a release candidate. We will address issues
and requests with a higher priority.

### Next Steps

1. Understand the [official documentation](https://cloud.google.com/service-infrastructure/docs/service-management/reference/rpc/google.longrunning/docs).
