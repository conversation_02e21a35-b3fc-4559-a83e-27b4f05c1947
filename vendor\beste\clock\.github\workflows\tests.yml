name: Tests

on:
  pull_request:
  push:
    branches:
      - '1.x'
      - '2.x'
      - '3.x'

jobs:
  tests:
    name: PHP ${{ matrix.php }}
    runs-on: ubuntu-20.04

    strategy:
      matrix:
        php:
          - "8.0"
          - "8.1"
          - "8.2"

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2
          coverage: xdebug
        env:
          COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: "ramsey/composer-install@v2"

      - name: Setup problem matchers for PHP
        run: echo "::add-matcher::${{ runner.tool_cache }}/php.json"

      - name: Run PHPStan
        run: vendor/bin/phpstan analyse --error-format=github

      - name: Run Psalm
        run: vendor/bin/psalm --output-format=github

      - name: Setup Problem Matchers for PHPUnit
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Run PHPUnit
        run: vendor/bin/phpunit --testdox --coverage-text
