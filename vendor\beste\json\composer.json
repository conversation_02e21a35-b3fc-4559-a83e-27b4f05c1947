{"name": "beste/json", "type": "library", "description": "A simple JSON helper to decode and encode JSON", "keywords": ["json", "helper"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "ext-json": "*"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpstan/phpstan": "^2.0.4", "phpstan/phpstan-phpunit": "^2.0.2", "phpstan/phpstan-strict-rules": "^2.0.1", "phpunit/phpunit": "^10.4.2", "rector/rector": "^2.0.3"}, "autoload": {"files": ["src/Json.php"]}, "autoload-dev": {"psr-4": {"Beste\\Json\\Tests\\": "tests"}}, "config": {"sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true}}, "scripts": {"analyse": ["XDEBUG_MODE=off vendor/bin/phpstan"], "analyze": "@analyse", "test": ["@analyse", "@test-units"], "test-units": "vendor/bin/phpunit"}}