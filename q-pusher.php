<?php

/**
 * Plugin Name: Q-Pusher
 * Description: Q-<PERSON>usher is a powerful plugin that seamlessly integrates with Formidable Forms, allowing you to send push notifications on form submissions.
 * With Q-Pusher, you can stay updated and connected with your users in real-time.
 * Version: 1.0.0
 * Author: Q-Ai
 */

if (version_compare(PHP_VERSION, '8.1.0', '<')) {
    add_action('admin_notices', function () {
        echo '<div class="error"><p>Q Pusher requires PHP 8.1 or higher. Your PHP version is ' . PHP_VERSION . '</p></div>';
    });
    return;
}

defined('ABSPATH') or die('No script kiddies please!');

// Temporarily suppress specific deprecation notices
error_reporting(E_ALL & ~E_DEPRECATED);

// Define constants for plugin directory and URL
define('Q_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('Q_PLUGIN_URL', plugin_dir_url(__FILE__));
define('Q_DB_VERSION', '1.1');

// Include required files
require_once Q_PLUGIN_DIR . 'includes/class-q-diagnostics.php';
require_once Q_PLUGIN_DIR . 'includes/notification-analytics.php';
require_once Q_PLUGIN_DIR . 'includes/firebase-functions.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-activator.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-settings.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-pwa-settings.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-pwa-manifest.php';

// Initialize settings
add_action('init', ['Q_Settings', 'init']);
add_action('init', ['Q_PWA_Settings', 'init']);
add_action('init', ['Q_PWA_Manifest', 'init']);

// Handle manifest.json requests early
add_action('init', 'q_handle_manifest_request', 1);

/**
 * Handle manifest.json requests directly
 */
function q_handle_manifest_request()
{
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';

    // Check if this is a manifest.json request
    if (
        strpos($request_uri, '/manifest.json') !== false ||
        (isset($_GET['q_manifest']) && $_GET['q_manifest'] == '1')
    ) {

        // Debug logging
        error_log('Q-PWA: Manifest request detected: ' . $request_uri);

        // Set headers
        header('Content-Type: application/json');
        header('Cache-Control: public, max-age=3600');
        header('Access-Control-Allow-Origin: *');

        // Generate manifest using the PWA Manifest class
        if (class_exists('Q_PWA_Manifest')) {
            $manifest = Q_PWA_Manifest::generate_manifest();
            error_log('Q-PWA: Generated manifest via class');
        } else {
            // Fallback basic manifest
            $manifest = [
                'name' => get_bloginfo('name'),
                'short_name' => get_bloginfo('name'),
                'description' => get_bloginfo('description'),
                'start_url' => '/',
                'display' => 'standalone',
                'theme_color' => '#000000',
                'background_color' => '#ffffff',
                'icons' => []
            ];
            error_log('Q-PWA: Generated fallback manifest');
        }

        echo json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        exit;
    }
}

// Change the conditional include to use proper hook
/**
 * Load Formidable Forms dependencies if the class exists.
 */
add_action('plugins_loaded', 'q_load_formidable_dependencies', 20);

/**
 * Load Formidable Forms dependencies if the class exists.
 */
function q_load_formidable_dependencies()
{
    if (class_exists('FrmFormAction')) {
        // Include Formidable actions if the class is available
        require_once Q_PLUGIN_DIR . 'includes/formidable-actions.php';
    } else {
        // Show admin notice if Formidable Forms is not active
        add_action('admin_notices', 'q_formidable_missing_notice');
    }
}

/**
 * Activation hook to set up the plugin.
 */
register_activation_hook(__FILE__, function () {
    Q_Activator::activate();
    q_activate();

    // Flush rewrite rules to ensure manifest.json works
    flush_rewrite_rules();
});

/**
 * Create the Q-Notify form and copy the service worker on activation.
 */
function q_activate()
{
    q_create_notify_form();
    q_copy_service_worker();
}

/**
 * Create the Q-Notify form if it doesn't exist.
 */
function q_create_notify_form()
{
    if (class_exists('FrmForm')) {
        global $wpdb;
        $form_name = 'Q-Notify';
        $form_exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$wpdb->prefix}frm_forms WHERE name = %s", $form_name));

        if (!$form_exists) {
            // Create form with specified values
            $form_values = array(
                'name' => $form_name,
                'description' => 'Form for sending notifications',
                'status' => 'published',
                'is_template' => 0,
                'form_key' => uniqid('qnotify_') // Add unique key
            );
            $form_id = FrmForm::create($form_values);

            if ($form_id) {
                q_add_fields_to_form($form_id);
            }
        }
    }
}

/**
 * Add fields to the Q-Notify form.
 */
function q_add_fields_to_form($form_id)
{
    $fields = array(
        array(
            'name' => 'Notification Type',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Enter the type of notification (e.g., Alert, Update, News, etc.)'
        ),
        array(
            'name' => 'Audience Type',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Specify the type of audience (e.g., All Users, Subscribers, Admins, etc.)'
        ),
        array(
            'name' => 'Audience',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Enter audience emails'
        )
    );

    // Add each field to the form
    foreach ($fields as $field) {
        FrmField::create($field);
    }
}

/**
 * Copy the service worker to the root directory.
 */
function q_copy_service_worker()
{
    $sw_source = Q_PLUGIN_DIR . 'firebase-messaging-sw.js';
    $sw_dest = ABSPATH . 'firebase-messaging-sw.js';

    // Debug logging
    error_log('Q-Pusher: Attempting to copy service worker');
    error_log('Q-Pusher: Source path: ' . $sw_source);
    error_log('Q-Pusher: Destination path: ' . $sw_dest);

    // Check if the source file exists
    if (!file_exists($sw_source)) {
        error_log('Q-Pusher: Service worker source file not found at ' . $sw_source);
        return false;
    }

    // Try to get Firebase configuration from JSON first
    $firebase_config = array();
    $json_config = get_option('q_firebase_config');

    if (!empty($json_config)) {
        $decoded = json_decode($json_config, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $firebase_config = $decoded;
        }
    }

    // If JSON config is not available or invalid, try individual settings
    if (empty($firebase_config) || !isset($firebase_config['projectId'])) {
        $firebase_config = array(
            'apiKey' => get_option('q_firebase_api_key'),
            'authDomain' => get_option('q_firebase_auth_domain'),
            'projectId' => get_option('q_firebase_project_id'),
            'storageBucket' => get_option('q_firebase_storage_bucket'),
            'messagingSenderId' => get_option('q_firebase_messaging_sender_id'),
            'appId' => get_option('q_firebase_app_id')
        );
    }

    // Verify Firebase configuration
    $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    foreach ($required_fields as $field) {
        if (empty($firebase_config[$field])) {
            error_log("Q-Pusher: Missing Firebase configuration: {$field}");
            return false;
        }
    }

    try {
        // Read the service worker template
        $sw_content = file_get_contents($sw_source);
        if ($sw_content === false) {
            throw new Exception('Failed to read service worker source file');
        }

        // Replace placeholders with actual values
        $sw_content = str_replace(
            [
                'FIREBASE_API_KEY',
                'FIREBASE_AUTH_DOMAIN',
                'FIREBASE_PROJECT_ID',
                'FIREBASE_STORAGE_BUCKET',
                'FIREBASE_MESSAGING_SENDER_ID',
                'FIREBASE_APP_ID',
                'CACHE_STRATEGY_PLACEHOLDER'
            ],
            [
                $firebase_config['apiKey'],
                $firebase_config['authDomain'],
                $firebase_config['projectId'],
                $firebase_config['storageBucket'],
                $firebase_config['messagingSenderId'],
                $firebase_config['appId'],
                get_option('q_pwa_cache_strategy', 'cache_first')
            ],
            $sw_content
        );

        // Ensure WordPress root directory is writable
        if (!is_writable(ABSPATH)) {
            throw new Exception('WordPress root directory is not writable');
        }

        // Delete existing service worker if it exists
        if (file_exists($sw_dest) && !unlink($sw_dest)) {
            throw new Exception('Failed to delete existing service worker file');
        }

        // Write the configured service worker
        if (file_put_contents($sw_dest, $sw_content) === false) {
            throw new Exception('Failed to write service worker file');
        }

        // Set proper permissions
        if (!chmod($sw_dest, 0644)) {
            throw new Exception('Failed to set service worker file permissions');
        }

        // Add rewrite rules
        add_rewrite_rule(
            '^firebase-messaging-sw\.js$',
            'index.php?q_service_worker=1',
            'top'
        );

        // Add manifest.json rewrite rule
        add_rewrite_rule(
            '^manifest\.json$',
            'index.php?q_manifest=1',
            'top'
        );

        // Flush rewrite rules
        flush_rewrite_rules();

        // Generate and copy customized offline.html file
        if (q_generate_offline_page()) {
            error_log('Q-Pusher: Offline page successfully generated and copied');
        } else {
            error_log('Q-Pusher: Failed to generate offline page');
        }

        error_log('Q-Pusher: Service worker successfully copied and configured');
        return true;

    } catch (Exception $e) {
        error_log('Q-Pusher Error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Deactivation hook to clean up on plugin deactivation.
 */
register_deactivation_hook(__FILE__, 'q_deactivate');

/**
 * Remove the service worker file on deactivation.
 */
function q_deactivate()
{
    // Remove service worker file
    $sw_file = ABSPATH . 'firebase-messaging-sw.js';
    if (file_exists($sw_file)) {
        unlink($sw_file);
    }

    // Remove offline.html file
    $offline_file = ABSPATH . 'offline.html';
    if (file_exists($offline_file)) {
        unlink($offline_file);
    }
}

/**
 * Enqueue scripts and styles for the frontend.
 */
add_action('wp_enqueue_scripts', 'q_enqueue_scripts');

/**
 * Enqueue scripts and styles for the frontend.
 */
function q_enqueue_scripts()
{
    q_enqueue_firebase_scripts();
    q_enqueue_custom_scripts();
}

/**
 * Enqueue Firebase SDK scripts.
 */
function q_enqueue_firebase_scripts()
{
    // Firebase SDK is now imported as a module in firebase-init.js
}

/**
 * Enqueue custom scripts for the plugin.
 */
function q_enqueue_custom_scripts()
{
    // Try to get Firebase configuration from JSON first
    $firebase_config = array();
    $json_config = get_option('q_firebase_config');

    if (!empty($json_config)) {
        $decoded = json_decode($json_config, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $firebase_config = $decoded;
        }
    }

    // If JSON config is not available or invalid, try individual settings
    if (empty($firebase_config) || !isset($firebase_config['projectId'])) {
        $firebase_config = array(
            'apiKey' => get_option('q_firebase_api_key'),
            'authDomain' => get_option('q_firebase_auth_domain'),
            'projectId' => get_option('q_firebase_project_id'),
            'storageBucket' => get_option('q_firebase_storage_bucket'),
            'messagingSenderId' => get_option('q_firebase_messaging_sender_id'),
            'appId' => get_option('q_firebase_app_id'),
            'measurementId' => get_option('q_firebase_measurement_id'),
            'publicVapidKey' => get_option('q_firebase_public_vapid_key'),
        );
    }

    // Verify required fields
    $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    $missing_fields = array();

    foreach ($required_fields as $field) {
        if (empty($firebase_config[$field])) {
            $missing_fields[] = $field;
        }
    }

    if (!empty($missing_fields)) {
        error_log('Q-Pusher: Missing required Firebase configuration fields: ' . implode(', ', $missing_fields));
    }

    // Add config as a global variable
    wp_register_script('q-firebase-config', '', array(), '', true);
    wp_enqueue_script('q-firebase-config');
    wp_add_inline_script(
        'q-firebase-config',
        'window.q_firebase_config = ' . json_encode($firebase_config) . ';',
        'before'
    );

    // Suppress jQuery migrate warnings
    wp_add_inline_script('jquery-migrate', 'jQuery.migrateMute = true;', 'before');

    // Add type="module" to script tags
    add_filter('script_loader_tag', function ($tag, $handle, $src) {
        if (in_array($handle, ['q-firebase-init', 'q-subscription', 'q-notification-badge'])) {
            $tag = '<script type="module" src="' . esc_url($src) . '"></script>';
        }
        return $tag;
    }, 10, 3);

    // Enqueue the module scripts
    wp_enqueue_script(
        'q-firebase-init',
        Q_PLUGIN_URL . 'includes/js/firebase-init.js',
        array('q-firebase-config'),
        '1.0.0',
        true
    );

    wp_enqueue_script(
        'q-subscription',
        Q_PLUGIN_URL . 'includes/js/subscription.js',
        array('q-firebase-init', 'jquery'),
        '1.0.0',
        true
    );

    // Enqueue the notification badge script
    wp_enqueue_script(
        'q-notification-badge',
        Q_PLUGIN_URL . 'includes/js/notification-badge.js',
        array('q-firebase-init'),
        '1.0.0',
        true
    );

    // Pass AJAX URL and nonce to JavaScript
    wp_localize_script('q-subscription', 'q_ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('q_subscribe_nonce')
    ));
}



/**
 * Add headers for the service worker to allow it to be served correctly.
 */
add_action('init', 'q_add_service_worker_headers');

/**
 * Add headers for service worker and handle requests
 */
function q_add_service_worker_headers()
{
    if (isset($_GET['q_service_worker'])) {
        header('Content-Type: application/javascript');
        header('Service-Worker-Allowed: /');
        header('Cache-Control: no-cache');

        $sw_path = ABSPATH . 'firebase-messaging-sw.js';
        if (file_exists($sw_path)) {
            readfile($sw_path);
        } else {
            error_log('Q-Pusher: Service worker file not found at: ' . $sw_path);
            status_header(404);
        }
        exit;
    }
}
add_action('init', 'q_add_service_worker_headers');

/**
 * Add query vars for service worker
 */
function q_add_query_vars($vars)
{
    $vars[] = 'q_service_worker';
    $vars[] = 'q_manifest';
    return $vars;
}
add_filter('query_vars', 'q_add_query_vars');

/**
 * Shortcode to display a subscription button for notifications.
 */
add_shortcode('q_subscribe_button', 'q_subscribe_button_shortcode');

/**
 * Shortcode to display a subscription button for notifications.
 */
function q_subscribe_button_shortcode()
{
    ob_start();
    ?>
    <button id="q-subscribe-button" class="ui-button primary"><i class="bi bi-bell-fill"></i> Subscribe to
        Notifications</button>
    <?php
    return ob_get_clean();
}

/**
 * Handle AJAX request to save the subscription token.
 */
add_action('wp_ajax_q_subscribe', 'q_subscribe_callback');
add_action('wp_ajax_nopriv_q_subscribe', 'q_subscribe_callback'); // For non-logged-in users

/**
 * Handle AJAX request to save the subscription token.
 */
function q_subscribe_callback()
{
    try {
        // Verify nonce checks
        if (!check_ajax_referer('q_subscribe_nonce', 'nonce', false)) {
            wp_send_json_error([
                'message' => 'Invalid security token'
            ]);
            return;
        }

        if (!check_ajax_referer('q_subscribe_nonce', 'security', false)) {
            wp_send_json_error([
                'message' => 'Secondary security check failed'
            ]);
            return;
        }

        // Validate token presence
        if (empty($_POST['token'])) {
            wp_send_json_error([
                'message' => 'Token is required'
            ]);
            return;
        }

        $token = sanitize_text_field($_POST['token']);

        // Validate token format
        if (strlen($token) < 50) {
            wp_send_json_error([
                'message' => 'Invalid token format'
            ]);
            return;
        }

        $user_id = get_current_user_id();

        if (!$user_id) {
            wp_send_json_error([
                'message' => 'User must be logged in'
            ]);
            return;
        }

        // Store the token
        $result = update_user_meta($user_id, 'q_push_token', $token);

        if ($result === false) {
            wp_send_json_error([
                'message' => 'Failed to save subscription'
            ]);
            return;
        }

        wp_send_json_success([
            'message' => 'Subscription successful!',
            'userId' => $user_id
        ]);

    } catch (Exception $e) {
        error_log('Subscription error: ' . $e->getMessage());
        wp_send_json_error([
            'message' => 'An unexpected error occurred'
        ]);
    }
}

/**
 * Display an admin notice if Formidable Forms is missing.
 */
function q_formidable_missing_notice()
{
    ?>
    <div class="notice notice-error is-dismissible">
        <p><?php _e('Q Pusher plugin requires Formidable Forms to be installed and activated.', 'formidable-firebase-push'); ?>
        </p>
    </div>
    <?php
}

// Add admin menu
add_action('admin_menu', 'q_add_admin_menu');

function q_add_admin_menu()
{
    add_menu_page(
        __('Q-Notify Subscribers', 'formidable-firebase-push'),
        __('Q-Notify', 'formidable-firebase-push'),
        'manage_options',
        'q-notify-subscribers',
        'q_render_subscribers_page',
        'dashicons-bell',
        30
    );
}

function q_render_subscribers_page()
{
    // Check user capabilities
    if (!current_user_can('manage_options')) {
        return;
    }

    // Get all subscribers
    $subscribers = get_users(array(
        'meta_key' => 'q_push_token',
        'meta_value' => '',
        'meta_compare' => '!='
    ));

    // Get analytics data
    $total_notifications = q_get_total_notifications();
    $engagement_rate = q_get_engagement_rate();

    // Include the template
    require_once Q_PLUGIN_DIR . 'includes/templates/subscribers-page.php';
}

// Add AJAX handler for removing subscribers
add_action('wp_ajax_q_remove_subscriber', 'q_remove_subscriber_callback');

function q_remove_subscriber_callback()
{
    check_ajax_referer('remove_subscriber_' . $_POST['user_id'], 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'formidable-firebase-push'));
        return;
    }

    $user_id = intval($_POST['user_id']);

    // Delete the push token
    $result = delete_user_meta($user_id, 'q_push_token');

    if ($result) {
        // Invalidate the token on Firebase if possible
        try {
            if (class_exists('Q_Firebase_Manager')) {
                Q_Firebase_Manager::invalidate_token($user_id);
            }
        } catch (Exception $e) {
            error_log('Failed to invalidate Firebase token: ' . $e->getMessage());
        }

        wp_send_json_success(__('Subscriber removed successfully.', 'formidable-firebase-push'));
    } else {
        wp_send_json_error(__('Failed to remove subscriber.', 'formidable-firebase-push'));
    }
}

// Add AJAX handler for sending test notifications
add_action('wp_ajax_q_send_test_notification', 'q_send_test_notification_callback');

function q_send_test_notification_callback()
{
    // Verify nonce
    $user_id = intval($_POST['user_id']);
    check_ajax_referer('test_notification_' . $user_id, 'nonce');

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'formidable-firebase-push'));
        return;
    }

    // Get the user's push token
    $token = get_user_meta($user_id, 'q_push_token', true);

    if (empty($token)) {
        wp_send_json_error(__('No push token found for this user.', 'formidable-firebase-push'));
        return;
    }

    // Send test notification
    $title = __('Test Notification', 'formidable-firebase-push');
    $message = __('This is a test notification from Q-Pusher.', 'formidable-firebase-push');

    $result = q_send_push_notification(
        $token,
        $title,
        $message,
        '', // No image
        true // Debug mode
    );

    if ($result) {
        wp_send_json_success(__('Test notification sent successfully.', 'formidable-firebase-push'));
    } else {
        wp_send_json_error(__('Failed to send test notification.', 'formidable-firebase-push'));
    }
}

// Add AJAX handler for clearing analytics
add_action('wp_ajax_q_clear_analytics', 'q_clear_analytics_callback');

function q_clear_analytics_callback()
{
    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'formidable-firebase-push'));
        return;
    }

    // Verify nonce
    check_ajax_referer('q_clear_analytics', 'nonce');

    global $wpdb;
    $table_name = $wpdb->prefix . 'q_notification_analytics';

    // Clear the analytics table
    $result = $wpdb->query("TRUNCATE TABLE $table_name");

    if ($result !== false) {
        wp_send_json_success(__('Analytics data cleared successfully.', 'formidable-firebase-push'));
    } else {
        wp_send_json_error(__('Failed to clear analytics data.', 'formidable-firebase-push'));
    }
}

// Helper functions for analytics
function q_get_total_notifications()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_notification_analytics';

    $count = $wpdb->get_var("
        SELECT COUNT(DISTINCT notification_id)
        FROM $table_name
        WHERE type = 'sent'
    ");

    return $count ? $count : 0;
}

function q_get_engagement_rate()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_notification_analytics';

    $total_sent = $wpdb->get_var("
        SELECT COUNT(DISTINCT notification_id)
        FROM $table_name
        WHERE type = 'sent'
    ");

    $total_clicks = $wpdb->get_var("
        SELECT COUNT(DISTINCT notification_id)
        FROM $table_name
        WHERE type = 'click'
    ");

    if (!$total_sent)
        return 0;
    return round(($total_clicks / $total_sent) * 100, 1);
}

function q_update_db_check()
{
    if (get_option('q_db_version') != Q_DB_VERSION) {
        Q_Activator::activate();
        update_option('q_db_version', Q_DB_VERSION);
    }
}
add_action('plugins_loaded', 'q_update_db_check');

add_action('admin_menu', function () {
    if (class_exists('Q_Diagnostics')) {
        add_submenu_page(
            'tools.php',
            'Q Pusher Diagnostics',
            'Q Pusher Diagnostics',
            'manage_options',
            'q-pusher-diagnostics',
            ['Q_Diagnostics', 'display_diagnostics_page']
        );
    }
});

/**
 * Verify service worker installation
 */
function q_verify_service_worker()
{
    $sw_path = ABSPATH . 'firebase-messaging-sw.js';
    if (!file_exists($sw_path)) {
        error_log('Q-Pusher: Service worker not found, attempting to reinstall');
        q_copy_service_worker();
    }
}
add_action('admin_init', 'q_verify_service_worker');

/**
 * Display admin notice for service worker issues
 */
function q_service_worker_admin_notice()
{
    if (!file_exists(ABSPATH . 'firebase-messaging-sw.js')) {
        // Add inline script for AJAX reinstall
        ?>
        <div class="error">
            <p>
                Q-Pusher: The Firebase service worker is not properly installed.
                <a href="<?php echo admin_url('tools.php?page=q-pusher-diagnostics'); ?>">
                    Run diagnostics
                </a>
                or
                <a href="#" id="q-reinstall-service-worker">
                    click here to reinstall
                </a>.
            </p>
            <div id="q-reinstall-result"
                style="display:none; margin-top: 10px; padding: 10px; background-color: #f8f8f8; border-left: 4px solid #46b450;">
            </div>
        </div>
        <script>
            jQuery(document).ready(function ($) {
                $('#q-reinstall-service-worker').on('click', function (e) {
                    e.preventDefault();

                    var $button = $(this);
                    var $result = $('#q-reinstall-result');

                    $button.text('Reinstalling...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'q_reinstall_service_worker',
                            nonce: '<?php echo wp_create_nonce('q_admin_nonce'); ?>'
                        },
                        success: function (response) {
                            if (response.success) {
                                $result.css('border-left-color', '#46b450').html('Service worker reinstalled successfully! Refresh the page to verify.');
                            } else {
                                $result.css('border-left-color', '#dc3232').html('Failed to reinstall service worker. Please check error logs or try the diagnostics page.');
                            }
                            $result.show();
                            $button.text('Click here to reinstall');
                        },
                        error: function () {
                            $result.css('border-left-color', '#dc3232').html('AJAX error occurred. Please try the diagnostics page instead.');
                            $result.show();
                            $button.text('Click here to reinstall');
                        }
                    });
                });
            });
        </script>
        <?php
    }
}
add_action('admin_notices', 'q_service_worker_admin_notice');

/**
 * Add AJAX handler for service worker reinstallation
 */
function q_handle_reinstall_service_worker()
{
    check_ajax_referer('q_admin_nonce', 'nonce');
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized');
        return;
    }

    $result = q_copy_service_worker();
    wp_send_json_success(['success' => $result]);
}
add_action('wp_ajax_q_reinstall_service_worker', 'q_handle_reinstall_service_worker');

/**
 * Handle AJAX request to verify subscription status
 */
add_action('wp_ajax_q_verify_subscription', 'q_verify_subscription_callback');
add_action('wp_ajax_nopriv_q_verify_subscription', 'q_verify_subscription_callback');

// Add AJAX handler for PWA events tracking
add_action('wp_ajax_q_track_pwa_event', 'q_track_pwa_event_callback');
add_action('wp_ajax_nopriv_q_track_pwa_event', 'q_track_pwa_event_callback');

function q_verify_subscription_callback()
{
    check_ajax_referer('q_subscribe_nonce', 'nonce');

    $token = sanitize_text_field($_POST['token']);
    $user_id = get_current_user_id();

    // Get stored token
    $stored_token = get_user_meta($user_id, 'q_push_token', true);

    wp_send_json_success([
        'isValid' => !empty($stored_token) && $stored_token === $token
    ]);
}

/**
 * Handle AJAX request to track PWA events
 */
function q_track_pwa_event_callback()
{
    // Verify nonce
    if (!check_ajax_referer('q_pwa_nonce', 'nonce', false)) {
        wp_send_json_error(['message' => 'Invalid security token']);
        return;
    }

    // Get event data
    $event = sanitize_text_field($_POST['event'] ?? '');
    $data = $_POST['data'] ?? [];

    if (empty($event)) {
        wp_send_json_error(['message' => 'Event name is required']);
        return;
    }

    // Sanitize event data
    $sanitized_data = [];
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $sanitized_data[sanitize_key($key)] = sanitize_text_field($value);
        }
    }

    // Store PWA event in database
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_pwa_analytics';

    $result = $wpdb->insert(
        $table_name,
        [
            'event_name' => $event,
            'event_data' => json_encode($sanitized_data),
            'user_id' => get_current_user_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'timestamp' => current_time('mysql')
        ],
        ['%s', '%s', '%d', '%s', '%s', '%s']
    );

    if ($result !== false) {
        wp_send_json_success(['message' => 'Event tracked successfully']);
    } else {
        wp_send_json_error(['message' => 'Failed to track event']);
    }
}

function q_validate_notification_payload($payload)
{
    $required_fields = ['title', 'message', 'token'];
    $errors = [];

    // Check required fields
    foreach ($required_fields as $field) {
        if (empty($payload[$field])) {
            $errors[] = "Missing required field: {$field}";
        }
    }

    // Validate token format
    if (!empty($payload['token']) && !preg_match('/^[a-zA-Z0-9:_-]{100,300}$/', $payload['token'])) {
        $errors[] = "Invalid token format";
    }

    // Validate title length
    if (strlen($payload['title']) > 100) {
        $errors[] = "Title exceeds maximum length of 100 characters";
    }

    // Validate message length
    if (strlen($payload['message']) > 2000) {
        $errors[] = "Message exceeds maximum length of 2000 characters";
    }

    // Validate image URL if present
    if (!empty($payload['image']) && !filter_var($payload['image'], FILTER_VALIDATE_URL)) {
        $errors[] = "Invalid image URL format";
    }

    return empty($errors) ? true : $errors;
}

function q_sanitize_input($data, $type = 'text')
{
    if (is_array($data)) {
        return array_map(function ($item) use ($type) {
            return q_sanitize_input($item, $type);
        }, $data);
    }

    $data = trim($data);

    switch ($type) {
        case 'email':
            return filter_var(sanitize_email($data), FILTER_VALIDATE_EMAIL) ? sanitize_email($data) : '';
        case 'url':
            return filter_var(esc_url_raw($data), FILTER_VALIDATE_URL) ? esc_url_raw($data) : '';
        case 'textarea':
            return wp_kses(sanitize_textarea_field($data), [
                'a' => ['href' => [], 'title' => []],
                'br' => [],
                'em' => [],
                'strong' => []
            ]);
        case 'key':
            return preg_replace('/[^a-zA-Z0-9_-]/', '', sanitize_key($data));
        case 'html':
            return wp_kses_post($data);
        case 'int':
            return filter_var($data, FILTER_VALIDATE_INT) !== false ? intval($data) : 0;
        case 'float':
            return filter_var($data, FILTER_VALIDATE_FLOAT) !== false ? floatval($data) : 0.0;
        case 'boolean':
            return filter_var($data, FILTER_VALIDATE_BOOLEAN);
        default:
            return sanitize_text_field($data);
    }
}

/**
 * Get the number of devices registered for a user
 *
 * @param int $user_id The user ID
 * @return int Number of registered devices
 */
function q_get_user_device_count($user_id)
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_devices';

    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
        $user_id
    ));

    return (int) $count;
}

/**
 * Generate a customized offline.html file based on PWA settings
 *
 * @return bool True on success, false on failure
 */
function q_generate_offline_page()
{
    try {
        // Get PWA settings
        $title = get_option('q_pwa_offline_title', 'You\'re Offline');
        $message = get_option('q_pwa_offline_message', 'It looks like you\'ve lost your internet connection. Don\'t worry, you can still browse some cached content!');
        $icon = get_option('q_pwa_offline_icon', '📡');
        $show_cached_pages = get_option('q_pwa_offline_show_cached_pages', true);
        $show_tips = get_option('q_pwa_offline_show_tips', true);
        $theme_color = get_option('q_pwa_theme_color', '#000000');
        $app_name = get_option('q_pwa_app_name', get_bloginfo('name'));

        // Generate the offline page HTML
        $offline_html = q_get_offline_page_template($title, $message, $icon, $show_cached_pages, $show_tips, $theme_color, $app_name);

        // Write to file
        $offline_dest = ABSPATH . 'offline.html';

        // Remove existing file if it exists
        if (file_exists($offline_dest)) {
            unlink($offline_dest);
        }

        // Write the new file
        if (file_put_contents($offline_dest, $offline_html) === false) {
            throw new Exception('Failed to write offline.html file');
        }

        // Set proper permissions
        if (!chmod($offline_dest, 0644)) {
            throw new Exception('Failed to set offline.html file permissions');
        }

        return true;

    } catch (Exception $e) {
        error_log('Q-Pusher: Error generating offline page: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get the offline page HTML template with customizations
 *
 * @param string $title Page title
 * @param string $message Page message
 * @param string $icon Page icon
 * @param bool $show_cached_pages Whether to show cached pages section
 * @param bool $show_tips Whether to show tips section
 * @param string $theme_color Theme color
 * @param string $app_name App name
 * @return string HTML content
 */
function q_get_offline_page_template($title, $message, $icon, $show_cached_pages, $show_tips, $theme_color, $app_name)
{
    $cached_pages_section = $show_cached_pages ? '
        <div class="cached-content">
            <h3>📚 Available Offline Content</h3>
            <ul class="cached-links" id="cachedLinks">
                <li><a href="/">🏠 Homepage</a></li>
                <li><a href="/about">ℹ️ About Us</a></li>
                <li><a href="/contact">📞 Contact</a></li>
            </ul>
        </div>' : '';

    $tips_section = $show_tips ? '
        <div class="tips">
            <h4>💡 Tips while offline:</h4>
            <ul>
                <li>Check your WiFi or mobile data connection</li>
                <li>Try moving to an area with better signal</li>
                <li>Some content may still be available from cache</li>
                <li>Your data will sync when you\'re back online</li>
            </ul>
        </div>' : '';

    return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . esc_html($title) . ' - ' . esc_html($app_name) . '</title>
    <meta name="description" content="You\'re currently offline. Please check your internet connection.">
    <meta name="theme-color" content="' . esc_attr($theme_color) . '">

    <!-- Inline critical CSS for offline page -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .offline-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, ' . esc_attr($theme_color) . ' 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .connection-status {
            margin-top: 25px;
            padding: 15px;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .status-offline {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .cached-content {
            margin-top: 30px;
            text-align: left;
        }

        .cached-content h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .cached-links {
            list-style: none;
        }

        .cached-links li {
            margin-bottom: 8px;
        }

        .cached-links a {
            color: ' . esc_attr($theme_color) . ';
            text-decoration: none;
            font-weight: 500;
        }

        .cached-links a:hover {
            text-decoration: underline;
        }

        .tips {
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: left;
        }

        .tips h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .tips ul {
            color: #666;
            padding-left: 20px;
        }

        .tips li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .offline-container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .offline-icon {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">' . esc_html($icon) . '</div>
        <h1>' . esc_html($title) . '</h1>
        <p class="subtitle">' . esc_html($message) . '</p>

        <div class="connection-status" id="connectionStatus">
            <span id="statusText">🔴 No internet connection detected</span>
        </div>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="retryConnection()">
                🔄 Try Again
            </button>
            <a href="/" class="btn btn-secondary">
                🏠 Go to Homepage
            </a>
        </div>

        ' . $cached_pages_section . '
        ' . $tips_section . '
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById(\'statusText\');
            const statusContainer = document.getElementById(\'connectionStatus\');

            if (navigator.onLine) {
                statusElement.textContent = \'🟢 Connection restored!\';
                statusContainer.className = \'connection-status status-online\';

                // Auto-reload after a short delay when back online
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.textContent = \'🔴 No internet connection detected\';
                statusContainer.className = \'connection-status status-offline\';
            }
        }

        // Retry connection
        function retryConnection() {
            const button = event.target;
            button.textContent = \'🔄 Checking...\';
            button.disabled = true;

            // Simulate checking connection
            setTimeout(() => {
                if (navigator.onLine) {
                    window.location.reload();
                } else {
                    button.textContent = \'🔄 Try Again\';
                    button.disabled = false;

                    // Show feedback
                    const statusElement = document.getElementById(\'statusText\');
                    statusElement.textContent = \'🔴 Still offline - please check your connection\';
                }
            }, 1500);
        }

        // Listen for online/offline events
        window.addEventListener(\'online\', updateConnectionStatus);
        window.addEventListener(\'offline\', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Load cached pages list from service worker if available
        if (\'serviceWorker\' in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
                type: \'GET_CACHED_PAGES\'
            });

            navigator.serviceWorker.addEventListener(\'message\', (event) => {
                if (event.data && event.data.type === \'CACHED_PAGES\') {
                    updateCachedLinks(event.data.pages);
                }
            });
        }

        function updateCachedLinks(pages) {
            const cachedLinksElement = document.getElementById(\'cachedLinks\');
            if (pages && pages.length > 0 && cachedLinksElement) {
                cachedLinksElement.innerHTML = pages.map(page =>
                    `<li><a href="${page.url}">${page.icon || \'📄\'} ${page.title || page.url}</a></li>`
                ).join(\'\');
            }
        }

        // Add some interactivity
        document.addEventListener(\'DOMContentLoaded\', () => {
            // Add click animation to buttons
            document.querySelectorAll(\'.btn\').forEach(btn => {
                btn.addEventListener(\'click\', function() {
                    this.style.transform = \'scale(0.95)\';
                    setTimeout(() => {
                        this.style.transform = \'\';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>';
}
