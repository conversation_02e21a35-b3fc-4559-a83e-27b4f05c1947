<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FeatureSet;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.protobuf.FeatureSet.EnumType</code>
 */
class EnumType
{
    /**
     * Generated from protobuf enum <code>ENUM_TYPE_UNKNOWN = 0;</code>
     */
    const ENUM_TYPE_UNKNOWN = 0;
    /**
     * Generated from protobuf enum <code>OPEN = 1;</code>
     */
    const OPEN = 1;
    /**
     * Generated from protobuf enum <code>CLOSED = 2;</code>
     */
    const CLOSED = 2;

    private static $valueToName = [
        self::ENUM_TYPE_UNKNOWN => 'ENUM_TYPE_UNKNOWN',
        self::OPEN => 'OPEN',
        self::CLOSED => 'CLOSED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

