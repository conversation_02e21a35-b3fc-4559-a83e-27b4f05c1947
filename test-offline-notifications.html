<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q-PWA Offline Notifications Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .test-button.secondary {
            background: #6c757d;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <h1>🔔 Q-PWA Offline Notifications Test</h1>
    
    <div class="test-section">
        <h2>Connection Status</h2>
        <div id="connection-status" class="status">
            Checking connection...
        </div>
        <p><strong>Connection Quality:</strong> <span id="connection-quality">Unknown</span></p>
    </div>

    <div class="test-section">
        <h2>Test Offline Notifications</h2>
        <p>Use these buttons to test the improved offline notification functionality:</p>
        
        <button class="test-button" onclick="testOfflineNotification()">
            📡 Show Offline Notification
        </button>
        
        <button class="test-button" onclick="testOnlineNotification()">
            ✅ Show Online Notification
        </button>
        
        <button class="test-button secondary" onclick="testSlowConnectionNotification()">
            🐌 Show Slow Connection Notification
        </button>
        
        <button class="test-button secondary" onclick="testRetryConnection()">
            🔄 Test Retry Connection
        </button>
        
        <button class="test-button danger" onclick="clearAllNotifications()">
            ❌ Clear All Notifications
        </button>
    </div>

    <div class="test-section">
        <h2>🚀 Improved Features</h2>
        <ul class="feature-list">
            <li><strong>Enhanced UI:</strong> Modern notification design with better typography and spacing</li>
            <li><strong>Interactive Actions:</strong> Retry button for connection testing and close button for manual dismissal</li>
            <li><strong>Connection Monitoring:</strong> Automatic detection of connection quality (good/fair/poor)</li>
            <li><strong>Smart Notifications:</strong> Different notification types for offline, online, and slow connection states</li>
            <li><strong>Customizable Messages:</strong> Admin-configurable titles, messages, and icons</li>
            <li><strong>Accessibility:</strong> ARIA labels, keyboard support (ESC to close), and screen reader friendly</li>
            <li><strong>Responsive Design:</strong> Mobile-optimized layout with touch-friendly buttons</li>
            <li><strong>Auto-hide Timer:</strong> Progress indicator for auto-dismissing notifications</li>
            <li><strong>Rate Limiting:</strong> Prevents notification spam with intelligent timing</li>
            <li><strong>Smooth Animations:</strong> CSS transitions for show/hide with proper easing</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📱 Mobile Testing</h2>
        <p>To test offline functionality on mobile:</p>
        <ol>
            <li>Open this page on your mobile device</li>
            <li>Turn on airplane mode or disable WiFi/cellular data</li>
            <li>Observe the offline notification behavior</li>
            <li>Turn connection back on to see the online notification</li>
        </ol>
    </div>

    <script>
        // Mock PWA settings for testing
        window.qPWASettings = {
            enabled: true,
            offlineEnabled: true,
            offlineNotificationEnabled: true,
            offlineTitle: 'You\'re Offline',
            offlineMessage: 'Some features may be limited while offline.',
            offlineIcon: '📡',
            connectionMonitoringEnabled: true,
            slowConnectionNotification: true,
            siteUrl: window.location.origin,
            ajaxUrl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce'
        };

        // Mock jQuery for testing
        if (typeof jQuery === 'undefined') {
            window.jQuery = {
                post: function(url, data) {
                    return {
                        done: function(callback) { return this; },
                        fail: function(callback) { return this; }
                    };
                }
            };
        }

        // Initialize PWA Manager for testing
        let pwaManager;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Load the PWA manager if available
            if (typeof QPWAManager !== 'undefined') {
                pwaManager = new QPWAManager();
            }
            
            updateConnectionStatus();
            
            // Monitor connection changes
            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);
        });

        function updateConnectionStatus() {
            const statusEl = document.getElementById('connection-status');
            const qualityEl = document.getElementById('connection-quality');
            
            if (navigator.onLine) {
                statusEl.textContent = 'Online';
                statusEl.className = 'status online';
                
                // Test connection quality
                testConnectionQuality().then(quality => {
                    qualityEl.textContent = quality;
                });
            } else {
                statusEl.textContent = 'Offline';
                statusEl.className = 'status offline';
                qualityEl.textContent = 'Offline';
            }
        }

        async function testConnectionQuality() {
            try {
                const startTime = Date.now();
                const response = await fetch(window.location.href + '?test=' + Date.now(), {
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (response.ok) {
                    if (responseTime < 1000) return 'Good';
                    if (responseTime < 3000) return 'Fair';
                    return 'Poor';
                }
                return 'Poor';
            } catch (error) {
                return 'Poor';
            }
        }

        function testOfflineNotification() {
            if (pwaManager) {
                pwaManager.showOfflineNotification();
            } else {
                alert('PWA Manager not loaded. This would show an offline notification.');
            }
        }

        function testOnlineNotification() {
            if (pwaManager) {
                pwaManager.showOnlineNotification();
            } else {
                alert('PWA Manager not loaded. This would show an online notification.');
            }
        }

        function testSlowConnectionNotification() {
            if (pwaManager) {
                pwaManager.showSlowConnectionNotification();
            } else {
                alert('PWA Manager not loaded. This would show a slow connection notification.');
            }
        }

        function testRetryConnection() {
            if (pwaManager) {
                pwaManager.retryConnection();
            } else {
                alert('PWA Manager not loaded. This would test the retry connection functionality.');
            }
        }

        function clearAllNotifications() {
            const notifications = document.querySelectorAll('.q-pwa-notification');
            notifications.forEach(notification => {
                notification.remove();
            });
        }
    </script>
</body>
</html>
