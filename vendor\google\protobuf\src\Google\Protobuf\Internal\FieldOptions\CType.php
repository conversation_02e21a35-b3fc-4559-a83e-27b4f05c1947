<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FieldOptions;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.protobuf.FieldOptions.CType</code>
 */
class CType
{
    /**
     * Default mode.
     *
     * Generated from protobuf enum <code>STRING = 0;</code>
     */
    const STRING = 0;
    /**
     * The option [ctype=CORD] may be applied to a non-repeated field of type
     * "bytes". It indicates that in C++, the data should be stored in a Cord
     * instead of a string.  For very large strings, this may reduce memory
     * fragmentation. It may also allow better performance when parsing from a
     * Cord, or when parsing with aliasing enabled, as the parsed Cord may then
     * alias the original buffer.
     *
     * Generated from protobuf enum <code>CORD = 1;</code>
     */
    const CORD = 1;
    /**
     * Generated from protobuf enum <code>STRING_PIECE = 2;</code>
     */
    const STRING_PIECE = 2;

    private static $valueToName = [
        self::STRING => 'STRING',
        self::CORD => 'CORD',
        self::STRING_PIECE => 'STRING_PIECE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

