<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FeatureSet;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.protobuf.FeatureSet.EnforceNamingStyle</code>
 */
class EnforceNamingStyle
{
    /**
     * Generated from protobuf enum <code>ENFORCE_NAMING_STYLE_UNKNOWN = 0;</code>
     */
    const ENFORCE_NAMING_STYLE_UNKNOWN = 0;
    /**
     * Generated from protobuf enum <code>STYLE2024 = 1;</code>
     */
    const STYLE2024 = 1;
    /**
     * Generated from protobuf enum <code>STYLE_LEGACY = 2;</code>
     */
    const STYLE_LEGACY = 2;

    private static $valueToName = [
        self::ENFORCE_NAMING_STYLE_UNKNOWN => 'ENFORCE_NAMING_STYLE_UNKNOWN',
        self::STYLE2024 => 'STYLE2024',
        self::STYLE_LEGACY => 'STYLE_LEGACY',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

