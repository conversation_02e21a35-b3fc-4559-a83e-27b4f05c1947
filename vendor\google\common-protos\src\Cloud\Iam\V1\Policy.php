<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/policy.proto

namespace Google\Cloud\Iam\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An Identity and Access Management (IAM) policy, which specifies access
 * controls for Google Cloud resources.
 * A `Policy` is a collection of `bindings`. A `binding` binds one or more
 * `members`, or principals, to a single `role`. Principals can be user
 * accounts, service accounts, Google groups, and domains (such as G Suite). A
 * `role` is a named list of permissions; each `role` can be an IAM predefined
 * role or a user-created custom role.
 * For some types of Google Cloud resources, a `binding` can also specify a
 * `condition`, which is a logical expression that allows access to a resource
 * only if the expression evaluates to `true`. A condition can add constraints
 * based on attributes of the request, the resource, or both. To learn which
 * resources support conditions in their IAM policies, see the
 * [IAM
 * documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
 * **JSON example:**
 * ```
 *     {
 *       "bindings": [
 *         {
 *           "role": "roles/resourcemanager.organizationAdmin",
 *           "members": [
 *             "user:mike&#64;example.com",
 *             "group:admins&#64;example.com",
 *             "domain:google.com",
 *             "serviceAccount:my-project-id&#64;appspot.gserviceaccount.com"
 *           ]
 *         },
 *         {
 *           "role": "roles/resourcemanager.organizationViewer",
 *           "members": [
 *             "user:eve&#64;example.com"
 *           ],
 *           "condition": {
 *             "title": "expirable access",
 *             "description": "Does not grant access after Sep 2020",
 *             "expression": "request.time <
 *             timestamp('2020-10-01T00:00:00.000Z')",
 *           }
 *         }
 *       ],
 *       "etag": "BwWWja0YfJA=",
 *       "version": 3
 *     }
 * ```
 * **YAML example:**
 * ```
 *     bindings:
 *     - members:
 *       - user:mike&#64;example.com
 *       - group:admins&#64;example.com
 *       - domain:google.com
 *       - serviceAccount:my-project-id&#64;appspot.gserviceaccount.com
 *       role: roles/resourcemanager.organizationAdmin
 *     - members:
 *       - user:eve&#64;example.com
 *       role: roles/resourcemanager.organizationViewer
 *       condition:
 *         title: expirable access
 *         description: Does not grant access after Sep 2020
 *         expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
 *     etag: BwWWja0YfJA=
 *     version: 3
 * ```
 * For a description of IAM and its features, see the
 * [IAM documentation](https://cloud.google.com/iam/docs/).
 *
 * Generated from protobuf message <code>google.iam.v1.Policy</code>
 */
class Policy extends \Google\Protobuf\Internal\Message
{
    /**
     * Specifies the format of the policy.
     * Valid values are `0`, `1`, and `3`. Requests that specify an invalid value
     * are rejected.
     * Any operation that affects conditional role bindings must specify version
     * `3`. This requirement applies to the following operations:
     * * Getting a policy that includes a conditional role binding
     * * Adding a conditional role binding to a policy
     * * Changing a conditional role binding in a policy
     * * Removing any role binding, with or without a condition, from a policy
     *   that includes conditions
     * **Important:** If you use IAM Conditions, you must include the `etag` field
     * whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     * you to overwrite a version `3` policy with a version `1` policy, and all of
     * the conditions in the version `3` policy are lost.
     * If a policy does not include any conditions, operations on that policy may
     * specify any valid version or leave the field unset.
     * To learn which resources support conditions in their IAM policies, see the
     * [IAM
     * documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
     *
     * Generated from protobuf field <code>int32 version = 1;</code>
     */
    protected $version = 0;
    /**
     * Associates a list of `members`, or principals, with a `role`. Optionally,
     * may specify a `condition` that determines how and when the `bindings` are
     * applied. Each of the `bindings` must contain at least one principal.
     * The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250
     * of these principals can be Google groups. Each occurrence of a principal
     * counts towards these limits. For example, if the `bindings` grant 50
     * different roles to `user:alice&#64;example.com`, and not to any other
     * principal, then you can add another 1,450 principals to the `bindings` in
     * the `Policy`.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.Binding bindings = 4;</code>
     */
    private $bindings;
    /**
     * Specifies cloud audit logging configuration for this policy.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.AuditConfig audit_configs = 6;</code>
     */
    private $audit_configs;
    /**
     * `etag` is used for optimistic concurrency control as a way to help
     * prevent simultaneous updates of a policy from overwriting each other.
     * It is strongly suggested that systems make use of the `etag` in the
     * read-modify-write cycle to perform policy updates in order to avoid race
     * conditions: An `etag` is returned in the response to `getIamPolicy`, and
     * systems are expected to put that etag in the request to `setIamPolicy` to
     * ensure that their change will be applied to the same version of the policy.
     * **Important:** If you use IAM Conditions, you must include the `etag` field
     * whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     * you to overwrite a version `3` policy with a version `1` policy, and all of
     * the conditions in the version `3` policy are lost.
     *
     * Generated from protobuf field <code>bytes etag = 3;</code>
     */
    protected $etag = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $version
     *           Specifies the format of the policy.
     *           Valid values are `0`, `1`, and `3`. Requests that specify an invalid value
     *           are rejected.
     *           Any operation that affects conditional role bindings must specify version
     *           `3`. This requirement applies to the following operations:
     *           * Getting a policy that includes a conditional role binding
     *           * Adding a conditional role binding to a policy
     *           * Changing a conditional role binding in a policy
     *           * Removing any role binding, with or without a condition, from a policy
     *             that includes conditions
     *           **Important:** If you use IAM Conditions, you must include the `etag` field
     *           whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     *           you to overwrite a version `3` policy with a version `1` policy, and all of
     *           the conditions in the version `3` policy are lost.
     *           If a policy does not include any conditions, operations on that policy may
     *           specify any valid version or leave the field unset.
     *           To learn which resources support conditions in their IAM policies, see the
     *           [IAM
     *           documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
     *     @type array<\Google\Cloud\Iam\V1\Binding>|\Google\Protobuf\Internal\RepeatedField $bindings
     *           Associates a list of `members`, or principals, with a `role`. Optionally,
     *           may specify a `condition` that determines how and when the `bindings` are
     *           applied. Each of the `bindings` must contain at least one principal.
     *           The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250
     *           of these principals can be Google groups. Each occurrence of a principal
     *           counts towards these limits. For example, if the `bindings` grant 50
     *           different roles to `user:alice&#64;example.com`, and not to any other
     *           principal, then you can add another 1,450 principals to the `bindings` in
     *           the `Policy`.
     *     @type array<\Google\Cloud\Iam\V1\AuditConfig>|\Google\Protobuf\Internal\RepeatedField $audit_configs
     *           Specifies cloud audit logging configuration for this policy.
     *     @type string $etag
     *           `etag` is used for optimistic concurrency control as a way to help
     *           prevent simultaneous updates of a policy from overwriting each other.
     *           It is strongly suggested that systems make use of the `etag` in the
     *           read-modify-write cycle to perform policy updates in order to avoid race
     *           conditions: An `etag` is returned in the response to `getIamPolicy`, and
     *           systems are expected to put that etag in the request to `setIamPolicy` to
     *           ensure that their change will be applied to the same version of the policy.
     *           **Important:** If you use IAM Conditions, you must include the `etag` field
     *           whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     *           you to overwrite a version `3` policy with a version `1` policy, and all of
     *           the conditions in the version `3` policy are lost.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\V1\Policy::initOnce();
        parent::__construct($data);
    }

    /**
     * Specifies the format of the policy.
     * Valid values are `0`, `1`, and `3`. Requests that specify an invalid value
     * are rejected.
     * Any operation that affects conditional role bindings must specify version
     * `3`. This requirement applies to the following operations:
     * * Getting a policy that includes a conditional role binding
     * * Adding a conditional role binding to a policy
     * * Changing a conditional role binding in a policy
     * * Removing any role binding, with or without a condition, from a policy
     *   that includes conditions
     * **Important:** If you use IAM Conditions, you must include the `etag` field
     * whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     * you to overwrite a version `3` policy with a version `1` policy, and all of
     * the conditions in the version `3` policy are lost.
     * If a policy does not include any conditions, operations on that policy may
     * specify any valid version or leave the field unset.
     * To learn which resources support conditions in their IAM policies, see the
     * [IAM
     * documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
     *
     * Generated from protobuf field <code>int32 version = 1;</code>
     * @return int
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * Specifies the format of the policy.
     * Valid values are `0`, `1`, and `3`. Requests that specify an invalid value
     * are rejected.
     * Any operation that affects conditional role bindings must specify version
     * `3`. This requirement applies to the following operations:
     * * Getting a policy that includes a conditional role binding
     * * Adding a conditional role binding to a policy
     * * Changing a conditional role binding in a policy
     * * Removing any role binding, with or without a condition, from a policy
     *   that includes conditions
     * **Important:** If you use IAM Conditions, you must include the `etag` field
     * whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     * you to overwrite a version `3` policy with a version `1` policy, and all of
     * the conditions in the version `3` policy are lost.
     * If a policy does not include any conditions, operations on that policy may
     * specify any valid version or leave the field unset.
     * To learn which resources support conditions in their IAM policies, see the
     * [IAM
     * documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
     *
     * Generated from protobuf field <code>int32 version = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setVersion($var)
    {
        GPBUtil::checkInt32($var);
        $this->version = $var;

        return $this;
    }

    /**
     * Associates a list of `members`, or principals, with a `role`. Optionally,
     * may specify a `condition` that determines how and when the `bindings` are
     * applied. Each of the `bindings` must contain at least one principal.
     * The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250
     * of these principals can be Google groups. Each occurrence of a principal
     * counts towards these limits. For example, if the `bindings` grant 50
     * different roles to `user:alice&#64;example.com`, and not to any other
     * principal, then you can add another 1,450 principals to the `bindings` in
     * the `Policy`.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.Binding bindings = 4;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getBindings()
    {
        return $this->bindings;
    }

    /**
     * Associates a list of `members`, or principals, with a `role`. Optionally,
     * may specify a `condition` that determines how and when the `bindings` are
     * applied. Each of the `bindings` must contain at least one principal.
     * The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250
     * of these principals can be Google groups. Each occurrence of a principal
     * counts towards these limits. For example, if the `bindings` grant 50
     * different roles to `user:alice&#64;example.com`, and not to any other
     * principal, then you can add another 1,450 principals to the `bindings` in
     * the `Policy`.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.Binding bindings = 4;</code>
     * @param array<\Google\Cloud\Iam\V1\Binding>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBindings($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\Iam\V1\Binding::class);
        $this->bindings = $arr;

        return $this;
    }

    /**
     * Specifies cloud audit logging configuration for this policy.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.AuditConfig audit_configs = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAuditConfigs()
    {
        return $this->audit_configs;
    }

    /**
     * Specifies cloud audit logging configuration for this policy.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.AuditConfig audit_configs = 6;</code>
     * @param array<\Google\Cloud\Iam\V1\AuditConfig>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAuditConfigs($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\Iam\V1\AuditConfig::class);
        $this->audit_configs = $arr;

        return $this;
    }

    /**
     * `etag` is used for optimistic concurrency control as a way to help
     * prevent simultaneous updates of a policy from overwriting each other.
     * It is strongly suggested that systems make use of the `etag` in the
     * read-modify-write cycle to perform policy updates in order to avoid race
     * conditions: An `etag` is returned in the response to `getIamPolicy`, and
     * systems are expected to put that etag in the request to `setIamPolicy` to
     * ensure that their change will be applied to the same version of the policy.
     * **Important:** If you use IAM Conditions, you must include the `etag` field
     * whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     * you to overwrite a version `3` policy with a version `1` policy, and all of
     * the conditions in the version `3` policy are lost.
     *
     * Generated from protobuf field <code>bytes etag = 3;</code>
     * @return string
     */
    public function getEtag()
    {
        return $this->etag;
    }

    /**
     * `etag` is used for optimistic concurrency control as a way to help
     * prevent simultaneous updates of a policy from overwriting each other.
     * It is strongly suggested that systems make use of the `etag` in the
     * read-modify-write cycle to perform policy updates in order to avoid race
     * conditions: An `etag` is returned in the response to `getIamPolicy`, and
     * systems are expected to put that etag in the request to `setIamPolicy` to
     * ensure that their change will be applied to the same version of the policy.
     * **Important:** If you use IAM Conditions, you must include the `etag` field
     * whenever you call `setIamPolicy`. If you omit this field, then IAM allows
     * you to overwrite a version `3` policy with a version `1` policy, and all of
     * the conditions in the version `3` policy are lost.
     *
     * Generated from protobuf field <code>bytes etag = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setEtag($var)
    {
        GPBUtil::checkString($var, False);
        $this->etag = $var;

        return $this;
    }

}

