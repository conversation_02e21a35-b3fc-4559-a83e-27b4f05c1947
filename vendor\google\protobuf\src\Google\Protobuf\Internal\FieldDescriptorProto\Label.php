<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FieldDescriptorProto;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.protobuf.FieldDescriptorProto.Label</code>
 */
class Label
{
    /**
     * 0 is reserved for errors
     *
     * Generated from protobuf enum <code>LABEL_OPTIONAL = 1;</code>
     */
    const LABEL_OPTIONAL = 1;
    /**
     * Generated from protobuf enum <code>LABEL_REPEATED = 3;</code>
     */
    const LABEL_REPEATED = 3;
    /**
     * The required label is only allowed in google.protobuf.  In proto3 and Editions
     * it's explicitly prohibited.  In Editions, the `field_presence` feature
     * can be used to get this behavior.
     *
     * Generated from protobuf enum <code>LABEL_REQUIRED = 2;</code>
     */
    const LABEL_REQUIRED = 2;

    private static $valueToName = [
        self::LABEL_OPTIONAL => 'LABEL_OPTIONAL',
        self::LABEL_REPEATED => 'LABEL_REPEATED',
        self::LABEL_REQUIRED => 'LABEL_REQUIRED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

