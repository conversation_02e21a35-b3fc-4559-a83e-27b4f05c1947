<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/client.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Settings for Java client libraries.
 *
 * Generated from protobuf message <code>google.api.JavaSettings</code>
 */
class JavaSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * The package name to use in Java. Clobbers the java_package option
     * set in the protobuf. This should be used **only** by APIs
     * who have already set the language_settings.java.package_name" field
     * in gapic.yaml. API teams should use the protobuf java_package option
     * where possible.
     * Example of a YAML configuration::
     *  publishing:
     *    java_settings:
     *      library_package: com.google.cloud.pubsub.v1
     *
     * Generated from protobuf field <code>string library_package = 1;</code>
     */
    protected $library_package = '';
    /**
     * Configure the Java class name to use instead of the service's for its
     * corresponding generated GAPIC client. Keys are fully-qualified
     * service names as they appear in the protobuf (including the full
     * the language_settings.java.interface_names" field in gapic.yaml. API
     * teams should otherwise use the service name as it appears in the
     * protobuf.
     * Example of a YAML configuration::
     *  publishing:
     *    java_settings:
     *      service_class_names:
     *        - google.pubsub.v1.Publisher: TopicAdmin
     *        - google.pubsub.v1.Subscriber: SubscriptionAdmin
     *
     * Generated from protobuf field <code>map<string, string> service_class_names = 2;</code>
     */
    private $service_class_names;
    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 3;</code>
     */
    protected $common = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $library_package
     *           The package name to use in Java. Clobbers the java_package option
     *           set in the protobuf. This should be used **only** by APIs
     *           who have already set the language_settings.java.package_name" field
     *           in gapic.yaml. API teams should use the protobuf java_package option
     *           where possible.
     *           Example of a YAML configuration::
     *            publishing:
     *              java_settings:
     *                library_package: com.google.cloud.pubsub.v1
     *     @type array|\Google\Protobuf\Internal\MapField $service_class_names
     *           Configure the Java class name to use instead of the service's for its
     *           corresponding generated GAPIC client. Keys are fully-qualified
     *           service names as they appear in the protobuf (including the full
     *           the language_settings.java.interface_names" field in gapic.yaml. API
     *           teams should otherwise use the service name as it appears in the
     *           protobuf.
     *           Example of a YAML configuration::
     *            publishing:
     *              java_settings:
     *                service_class_names:
     *                  - google.pubsub.v1.Publisher: TopicAdmin
     *                  - google.pubsub.v1.Subscriber: SubscriptionAdmin
     *     @type \Google\Api\CommonLanguageSettings $common
     *           Some settings.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Client::initOnce();
        parent::__construct($data);
    }

    /**
     * The package name to use in Java. Clobbers the java_package option
     * set in the protobuf. This should be used **only** by APIs
     * who have already set the language_settings.java.package_name" field
     * in gapic.yaml. API teams should use the protobuf java_package option
     * where possible.
     * Example of a YAML configuration::
     *  publishing:
     *    java_settings:
     *      library_package: com.google.cloud.pubsub.v1
     *
     * Generated from protobuf field <code>string library_package = 1;</code>
     * @return string
     */
    public function getLibraryPackage()
    {
        return $this->library_package;
    }

    /**
     * The package name to use in Java. Clobbers the java_package option
     * set in the protobuf. This should be used **only** by APIs
     * who have already set the language_settings.java.package_name" field
     * in gapic.yaml. API teams should use the protobuf java_package option
     * where possible.
     * Example of a YAML configuration::
     *  publishing:
     *    java_settings:
     *      library_package: com.google.cloud.pubsub.v1
     *
     * Generated from protobuf field <code>string library_package = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setLibraryPackage($var)
    {
        GPBUtil::checkString($var, True);
        $this->library_package = $var;

        return $this;
    }

    /**
     * Configure the Java class name to use instead of the service's for its
     * corresponding generated GAPIC client. Keys are fully-qualified
     * service names as they appear in the protobuf (including the full
     * the language_settings.java.interface_names" field in gapic.yaml. API
     * teams should otherwise use the service name as it appears in the
     * protobuf.
     * Example of a YAML configuration::
     *  publishing:
     *    java_settings:
     *      service_class_names:
     *        - google.pubsub.v1.Publisher: TopicAdmin
     *        - google.pubsub.v1.Subscriber: SubscriptionAdmin
     *
     * Generated from protobuf field <code>map<string, string> service_class_names = 2;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getServiceClassNames()
    {
        return $this->service_class_names;
    }

    /**
     * Configure the Java class name to use instead of the service's for its
     * corresponding generated GAPIC client. Keys are fully-qualified
     * service names as they appear in the protobuf (including the full
     * the language_settings.java.interface_names" field in gapic.yaml. API
     * teams should otherwise use the service name as it appears in the
     * protobuf.
     * Example of a YAML configuration::
     *  publishing:
     *    java_settings:
     *      service_class_names:
     *        - google.pubsub.v1.Publisher: TopicAdmin
     *        - google.pubsub.v1.Subscriber: SubscriptionAdmin
     *
     * Generated from protobuf field <code>map<string, string> service_class_names = 2;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setServiceClassNames($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->service_class_names = $arr;

        return $this;
    }

    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 3;</code>
     * @return \Google\Api\CommonLanguageSettings|null
     */
    public function getCommon()
    {
        return $this->common;
    }

    public function hasCommon()
    {
        return isset($this->common);
    }

    public function clearCommon()
    {
        unset($this->common);
    }

    /**
     * Some settings.
     *
     * Generated from protobuf field <code>.google.api.CommonLanguageSettings common = 3;</code>
     * @param \Google\Api\CommonLanguageSettings $var
     * @return $this
     */
    public function setCommon($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\CommonLanguageSettings::class);
        $this->common = $var;

        return $this;
    }

}

