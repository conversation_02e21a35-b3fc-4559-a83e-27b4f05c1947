<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You're Offline - Q-AI PWA</title>
    <meta name="description" content="You're currently offline. Please check your internet connection.">
    <meta name="theme-color" content="#000000">
    
    <!-- Inline critical CSS for offline page -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .offline-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .connection-status {
            margin-top: 25px;
            padding: 15px;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .status-offline {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .cached-content {
            margin-top: 30px;
            text-align: left;
        }

        .cached-content h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .cached-links {
            list-style: none;
        }

        .cached-links li {
            margin-bottom: 8px;
        }

        .cached-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .cached-links a:hover {
            text-decoration: underline;
        }

        .tips {
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: left;
        }

        .tips h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .tips ul {
            color: #666;
            padding-left: 20px;
        }

        .tips li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .offline-container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .offline-icon {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1>You're Offline</h1>
        <p class="subtitle">It looks like you've lost your internet connection. Don't worry, you can still browse some cached content!</p>
        
        <div class="connection-status" id="connectionStatus">
            <span id="statusText">🔴 No internet connection detected</span>
        </div>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="retryConnection()">
                🔄 Try Again
            </button>
            <a href="/" class="btn btn-secondary">
                🏠 Go to Homepage
            </a>
        </div>

        <div class="cached-content">
            <h3>📚 Available Offline Content</h3>
            <ul class="cached-links" id="cachedLinks">
                <li><a href="/">🏠 Homepage</a></li>
                <li><a href="/about">ℹ️ About Us</a></li>
                <li><a href="/contact">📞 Contact</a></li>
            </ul>
        </div>

        <div class="tips">
            <h4>💡 Tips while offline:</h4>
            <ul>
                <li>Check your WiFi or mobile data connection</li>
                <li>Try moving to an area with better signal</li>
                <li>Some content may still be available from cache</li>
                <li>Your data will sync when you're back online</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('statusText');
            const statusContainer = document.getElementById('connectionStatus');

            if (navigator.onLine) {
                statusElement.textContent = '🟢 Connection restored!';
                statusContainer.className = 'connection-status status-online';

                // Auto-reload after a short delay when back online
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.textContent = '🔴 No internet connection detected';
                statusContainer.className = 'connection-status status-offline';
            }
        }

        // Retry connection
        function retryConnection() {
            const button = event.target;
            button.textContent = '🔄 Checking...';
            button.disabled = true;

            // Simulate checking connection
            setTimeout(() => {
                if (navigator.onLine) {
                    window.location.reload();
                } else {
                    button.textContent = '🔄 Try Again';
                    button.disabled = false;

                    // Show feedback
                    const statusElement = document.getElementById('statusText');
                    statusElement.textContent = '🔴 Still offline - please check your connection';
                }
            }, 1500);
        }

        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Load cached pages list from service worker if available
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
                type: 'GET_CACHED_PAGES'
            });

            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'CACHED_PAGES') {
                    updateCachedLinks(event.data.pages);
                }
            });
        }

        function updateCachedLinks(pages) {
            const cachedLinksElement = document.getElementById('cachedLinks');
            if (pages && pages.length > 0) {
                cachedLinksElement.innerHTML = pages.map(page =>
                    `<li><a href="${page.url}">${page.icon || '📄'} ${page.title || page.url}</a></li>`
                ).join('');
            }
        }

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', () => {
            // Add click animation to buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
