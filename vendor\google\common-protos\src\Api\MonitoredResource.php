<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/monitored_resource.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An object representing a resource that can be used for monitoring, logging,
 * billing, or other purposes. Examples include virtual machine instances,
 * databases, and storage devices such as disks. The `type` field identifies a
 * [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor] object
 * that describes the resource's schema. Information in the `labels` field
 * identifies the actual resource and its attributes according to the schema.
 * For example, a particular Compute Engine VM instance could be represented by
 * the following object, because the
 * [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor] for
 * `"gce_instance"` has labels
 * `"project_id"`, `"instance_id"` and `"zone"`:
 *     { "type": "gce_instance",
 *       "labels": { "project_id": "my-project",
 *                   "instance_id": "12345678901234",
 *                   "zone": "us-central1-a" }}
 *
 * Generated from protobuf message <code>google.api.MonitoredResource</code>
 */
class MonitoredResource extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The monitored resource type. This field must match
     * the `type` field of a
     * [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor]
     * object. For example, the type of a Compute Engine VM instance is
     * `gce_instance`. Some descriptors include the service name in the type; for
     * example, the type of a Datastream stream is
     * `datastream.googleapis.com/Stream`.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     */
    protected $type = '';
    /**
     * Required. Values for all of the labels listed in the associated monitored
     * resource descriptor. For example, Compute Engine VM instances use the
     * labels `"project_id"`, `"instance_id"`, and `"zone"`.
     *
     * Generated from protobuf field <code>map<string, string> labels = 2;</code>
     */
    private $labels;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $type
     *           Required. The monitored resource type. This field must match
     *           the `type` field of a
     *           [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor]
     *           object. For example, the type of a Compute Engine VM instance is
     *           `gce_instance`. Some descriptors include the service name in the type; for
     *           example, the type of a Datastream stream is
     *           `datastream.googleapis.com/Stream`.
     *     @type array|\Google\Protobuf\Internal\MapField $labels
     *           Required. Values for all of the labels listed in the associated monitored
     *           resource descriptor. For example, Compute Engine VM instances use the
     *           labels `"project_id"`, `"instance_id"`, and `"zone"`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\MonitoredResource::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The monitored resource type. This field must match
     * the `type` field of a
     * [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor]
     * object. For example, the type of a Compute Engine VM instance is
     * `gce_instance`. Some descriptors include the service name in the type; for
     * example, the type of a Datastream stream is
     * `datastream.googleapis.com/Stream`.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Required. The monitored resource type. This field must match
     * the `type` field of a
     * [MonitoredResourceDescriptor][google.api.MonitoredResourceDescriptor]
     * object. For example, the type of a Compute Engine VM instance is
     * `gce_instance`. Some descriptors include the service name in the type; for
     * example, the type of a Datastream stream is
     * `datastream.googleapis.com/Stream`.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

    /**
     * Required. Values for all of the labels listed in the associated monitored
     * resource descriptor. For example, Compute Engine VM instances use the
     * labels `"project_id"`, `"instance_id"`, and `"zone"`.
     *
     * Generated from protobuf field <code>map<string, string> labels = 2;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getLabels()
    {
        return $this->labels;
    }

    /**
     * Required. Values for all of the labels listed in the associated monitored
     * resource descriptor. For example, Compute Engine VM instances use the
     * labels `"project_id"`, `"instance_id"`, and `"zone"`.
     *
     * Generated from protobuf field <code>map<string, string> labels = 2;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setLabels($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->labels = $arr;

        return $this;
    }

}

