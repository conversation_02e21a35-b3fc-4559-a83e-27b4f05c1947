<?php
/**
 * Test file to verify that offline notification settings are properly registered and can be saved
 * This file can be run in a WordPress environment to test the fix
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll simulate WordPress environment
    define('ABSPATH', dirname(__FILE__) . '/');
}

/**
 * Test function to verify offline notification settings registration
 */
function test_offline_notification_settings() {
    echo "<h2>Testing Offline Notification Settings Fix</h2>\n";
    
    // List of offline notification settings that should be registered
    $offline_notification_settings = [
        'q_pwa_offline_notification_enabled',
        'q_pwa_offline_notification_title', 
        'q_pwa_offline_notification_message',
        'q_pwa_offline_notification_icon',
        'q_pwa_connection_monitoring_enabled',
        'q_pwa_slow_connection_notification'
    ];
    
    echo "<h3>1. Checking if settings are registered with WordPress Settings API:</h3>\n";
    
    foreach ($offline_notification_settings as $setting) {
        // Check if setting is registered
        $registered_settings = get_registered_settings();
        $is_registered = isset($registered_settings[$setting]);
        
        $status = $is_registered ? "✅ REGISTERED" : "❌ NOT REGISTERED";
        echo "<p><strong>{$setting}:</strong> {$status}</p>\n";
        
        if ($is_registered) {
            $setting_data = $registered_settings[$setting];
            echo "<ul>\n";
            echo "<li>Type: " . ($setting_data['type'] ?? 'unknown') . "</li>\n";
            echo "<li>Default: " . ($setting_data['default'] ?? 'none') . "</li>\n";
            echo "<li>Sanitize Callback: " . (is_callable($setting_data['sanitize_callback']) ? 'Yes' : 'No') . "</li>\n";
            echo "</ul>\n";
        }
    }
    
    echo "<h3>2. Testing setting values (current values in database):</h3>\n";
    
    foreach ($offline_notification_settings as $setting) {
        $value = get_option($setting);
        $display_value = is_bool($value) ? ($value ? 'true' : 'false') : $value;
        if (empty($display_value) && $display_value !== '0' && $display_value !== false) {
            $display_value = '(empty/default)';
        }
        echo "<p><strong>{$setting}:</strong> {$display_value}</p>\n";
    }
    
    echo "<h3>3. Testing if settings fields are added to admin page:</h3>\n";
    
    // Check if Q_PWA_Settings class exists and has the required methods
    if (class_exists('Q_PWA_Settings')) {
        echo "<p>✅ Q_PWA_Settings class exists</p>\n";
        
        // Check if the render methods exist
        $render_methods = [
            'render_checkbox_field',
            'render_text_field', 
            'render_textarea_field'
        ];
        
        foreach ($render_methods as $method) {
            if (method_exists('Q_PWA_Settings', $method)) {
                echo "<p>✅ Method {$method} exists</p>\n";
            } else {
                echo "<p>❌ Method {$method} missing</p>\n";
            }
        }
    } else {
        echo "<p>❌ Q_PWA_Settings class not found</p>\n";
    }
    
    echo "<h3>4. Simulating form submission test:</h3>\n";
    
    // Test setting and getting values
    $test_values = [
        'q_pwa_offline_notification_enabled' => true,
        'q_pwa_offline_notification_title' => 'Test Offline Title',
        'q_pwa_offline_notification_message' => 'Test offline message for notifications',
        'q_pwa_offline_notification_icon' => '🔌',
        'q_pwa_connection_monitoring_enabled' => false,
        'q_pwa_slow_connection_notification' => true
    ];
    
    echo "<p>Setting test values...</p>\n";
    foreach ($test_values as $setting => $value) {
        $result = update_option($setting, $value);
        $status = $result ? "✅ SAVED" : "❌ FAILED";
        echo "<p>{$setting}: {$status}</p>\n";
    }
    
    echo "<p>Retrieving test values...</p>\n";
    foreach ($test_values as $setting => $expected_value) {
        $actual_value = get_option($setting);
        $match = ($actual_value == $expected_value) ? "✅ MATCH" : "❌ MISMATCH";
        $display_expected = is_bool($expected_value) ? ($expected_value ? 'true' : 'false') : $expected_value;
        $display_actual = is_bool($actual_value) ? ($actual_value ? 'true' : 'false') : $actual_value;
        echo "<p>{$setting}: Expected '{$display_expected}', Got '{$display_actual}' - {$match}</p>\n";
    }
    
    echo "<h3>5. Testing JavaScript localization:</h3>\n";
    
    // Check if the settings are properly passed to JavaScript
    if (class_exists('Q_PWA_Settings') && method_exists('Q_PWA_Settings', 'enqueue_pwa_scripts')) {
        echo "<p>✅ enqueue_pwa_scripts method exists</p>\n";
        echo "<p>The offline notification settings should be included in the qPWASettings JavaScript object</p>\n";
        echo "<p>Check browser console for qPWASettings.offlineNotificationEnabled, etc.</p>\n";
    } else {
        echo "<p>❌ enqueue_pwa_scripts method not found</p>\n";
    }
    
    echo "<h3>Summary:</h3>\n";
    echo "<p>If all settings show as REGISTERED and SAVED/MATCH, then the fix is working correctly.</p>\n";
    echo "<p>The offline notification settings should now save properly when the PWA settings form is submitted.</p>\n";
}

// Run the test if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    // Simple HTML wrapper for better display
    echo "<!DOCTYPE html>\n<html>\n<head>\n<title>Offline Settings Test</title>\n</head>\n<body>\n";
    
    if (function_exists('get_option')) {
        test_offline_notification_settings();
    } else {
        echo "<h2>WordPress Environment Required</h2>\n";
        echo "<p>This test needs to be run in a WordPress environment where WordPress functions are available.</p>\n";
        echo "<p>You can include this file in a WordPress page or run it through WP-CLI.</p>\n";
    }
    
    echo "\n</body>\n</html>";
}
?>
