<?php
class Q_Firebase_Manager {
    /**
     * Invalidate a user's Firebase token
     */
    public static function invalidate_token($user_id) {
        try {
            // Get the Firebase Auth instance
            $auth = self::get_firebase_auth();
            if (!$auth) {
                throw new Exception('Firebase Auth not initialized');
            }

            // Get the stored token
            $token = get_user_meta($user_id, 'q_push_token', true);
            if (empty($token)) {
                return true; // Token already removed
            }

            // Revoke all refresh tokens for the user
            // This will force the client to re-authenticate
            $auth->revokeRefreshTokens($token);

            return true;
        } catch (Exception $e) {
            error_log('Firebase token invalidation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get Firebase Auth instance
     */
    private static function get_firebase_auth() {
        static $auth = null;

        if ($auth === null) {
            try {
                $firebase = self::initialize_firebase();
                $auth = $firebase->getAuth();
            } catch (Exception $e) {
                error_log('Firebase Auth initialization failed: ' . $e->getMessage());
                return null;
            }
        }

        return $auth;
    }

    /**
     * Initialize Firebase
     */
    private static function initialize_firebase() {
        $firebase_config = get_option('q_firebase_config');
        
        if (empty($firebase_config)) {
            throw new Exception('Firebase configuration not found in settings');
        }

        $factory = (new Factory)->withServiceAccount($firebase_config);
        return $factory->createAuth();
    }
}
