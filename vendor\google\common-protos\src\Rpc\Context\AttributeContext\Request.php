<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/context/attribute_context.proto

namespace Google\Rpc\Context\AttributeContext;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * This message defines attributes for an HTTP request. If the actual
 * request is not an HTTP request, the runtime system should try to map
 * the actual request to an equivalent HTTP request.
 *
 * Generated from protobuf message <code>google.rpc.context.AttributeContext.Request</code>
 */
class Request extends \Google\Protobuf\Internal\Message
{
    /**
     * The unique ID for a request, which can be propagated to downstream
     * systems. The ID should have low probability of collision
     * within a single day for a specific service.
     *
     * Generated from protobuf field <code>string id = 1;</code>
     */
    protected $id = '';
    /**
     * The HTTP request method, such as `GET`, `POST`.
     *
     * Generated from protobuf field <code>string method = 2;</code>
     */
    protected $method = '';
    /**
     * The HTTP request headers. If multiple headers share the same key, they
     * must be merged according to the HTTP spec. All header keys must be
     * lowercased, because HTTP header keys are case-insensitive.
     *
     * Generated from protobuf field <code>map<string, string> headers = 3;</code>
     */
    private $headers;
    /**
     * The HTTP URL path, excluding the query parameters.
     *
     * Generated from protobuf field <code>string path = 4;</code>
     */
    protected $path = '';
    /**
     * The HTTP request `Host` header value.
     *
     * Generated from protobuf field <code>string host = 5;</code>
     */
    protected $host = '';
    /**
     * The HTTP URL scheme, such as `http` and `https`.
     *
     * Generated from protobuf field <code>string scheme = 6;</code>
     */
    protected $scheme = '';
    /**
     * The HTTP URL query in the format of `name1=value1&name2=value2`, as it
     * appears in the first line of the HTTP request. No decoding is performed.
     *
     * Generated from protobuf field <code>string query = 7;</code>
     */
    protected $query = '';
    /**
     * The timestamp when the `destination` service receives the last byte of
     * the request.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp time = 9;</code>
     */
    protected $time = null;
    /**
     * The HTTP request size in bytes. If unknown, it must be -1.
     *
     * Generated from protobuf field <code>int64 size = 10;</code>
     */
    protected $size = 0;
    /**
     * The network protocol used with the request, such as "http/1.1",
     * "spdy/3", "h2", "h2c", "webrtc", "tcp", "udp", "quic". See
     * https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids
     * for details.
     *
     * Generated from protobuf field <code>string protocol = 11;</code>
     */
    protected $protocol = '';
    /**
     * A special parameter for request reason. It is used by security systems
     * to associate auditing information with a request.
     *
     * Generated from protobuf field <code>string reason = 12;</code>
     */
    protected $reason = '';
    /**
     * The request authentication. May be absent for unauthenticated requests.
     * Derived from the HTTP request `Authorization` header or equivalent.
     *
     * Generated from protobuf field <code>.google.rpc.context.AttributeContext.Auth auth = 13;</code>
     */
    protected $auth = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $id
     *           The unique ID for a request, which can be propagated to downstream
     *           systems. The ID should have low probability of collision
     *           within a single day for a specific service.
     *     @type string $method
     *           The HTTP request method, such as `GET`, `POST`.
     *     @type array|\Google\Protobuf\Internal\MapField $headers
     *           The HTTP request headers. If multiple headers share the same key, they
     *           must be merged according to the HTTP spec. All header keys must be
     *           lowercased, because HTTP header keys are case-insensitive.
     *     @type string $path
     *           The HTTP URL path, excluding the query parameters.
     *     @type string $host
     *           The HTTP request `Host` header value.
     *     @type string $scheme
     *           The HTTP URL scheme, such as `http` and `https`.
     *     @type string $query
     *           The HTTP URL query in the format of `name1=value1&name2=value2`, as it
     *           appears in the first line of the HTTP request. No decoding is performed.
     *     @type \Google\Protobuf\Timestamp $time
     *           The timestamp when the `destination` service receives the last byte of
     *           the request.
     *     @type int|string $size
     *           The HTTP request size in bytes. If unknown, it must be -1.
     *     @type string $protocol
     *           The network protocol used with the request, such as "http/1.1",
     *           "spdy/3", "h2", "h2c", "webrtc", "tcp", "udp", "quic". See
     *           https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids
     *           for details.
     *     @type string $reason
     *           A special parameter for request reason. It is used by security systems
     *           to associate auditing information with a request.
     *     @type \Google\Rpc\Context\AttributeContext\Auth $auth
     *           The request authentication. May be absent for unauthenticated requests.
     *           Derived from the HTTP request `Authorization` header or equivalent.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Rpc\Context\AttributeContext::initOnce();
        parent::__construct($data);
    }

    /**
     * The unique ID for a request, which can be propagated to downstream
     * systems. The ID should have low probability of collision
     * within a single day for a specific service.
     *
     * Generated from protobuf field <code>string id = 1;</code>
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * The unique ID for a request, which can be propagated to downstream
     * systems. The ID should have low probability of collision
     * within a single day for a specific service.
     *
     * Generated from protobuf field <code>string id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkString($var, True);
        $this->id = $var;

        return $this;
    }

    /**
     * The HTTP request method, such as `GET`, `POST`.
     *
     * Generated from protobuf field <code>string method = 2;</code>
     * @return string
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * The HTTP request method, such as `GET`, `POST`.
     *
     * Generated from protobuf field <code>string method = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setMethod($var)
    {
        GPBUtil::checkString($var, True);
        $this->method = $var;

        return $this;
    }

    /**
     * The HTTP request headers. If multiple headers share the same key, they
     * must be merged according to the HTTP spec. All header keys must be
     * lowercased, because HTTP header keys are case-insensitive.
     *
     * Generated from protobuf field <code>map<string, string> headers = 3;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getHeaders()
    {
        return $this->headers;
    }

    /**
     * The HTTP request headers. If multiple headers share the same key, they
     * must be merged according to the HTTP spec. All header keys must be
     * lowercased, because HTTP header keys are case-insensitive.
     *
     * Generated from protobuf field <code>map<string, string> headers = 3;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setHeaders($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->headers = $arr;

        return $this;
    }

    /**
     * The HTTP URL path, excluding the query parameters.
     *
     * Generated from protobuf field <code>string path = 4;</code>
     * @return string
     */
    public function getPath()
    {
        return $this->path;
    }

    /**
     * The HTTP URL path, excluding the query parameters.
     *
     * Generated from protobuf field <code>string path = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setPath($var)
    {
        GPBUtil::checkString($var, True);
        $this->path = $var;

        return $this;
    }

    /**
     * The HTTP request `Host` header value.
     *
     * Generated from protobuf field <code>string host = 5;</code>
     * @return string
     */
    public function getHost()
    {
        return $this->host;
    }

    /**
     * The HTTP request `Host` header value.
     *
     * Generated from protobuf field <code>string host = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setHost($var)
    {
        GPBUtil::checkString($var, True);
        $this->host = $var;

        return $this;
    }

    /**
     * The HTTP URL scheme, such as `http` and `https`.
     *
     * Generated from protobuf field <code>string scheme = 6;</code>
     * @return string
     */
    public function getScheme()
    {
        return $this->scheme;
    }

    /**
     * The HTTP URL scheme, such as `http` and `https`.
     *
     * Generated from protobuf field <code>string scheme = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setScheme($var)
    {
        GPBUtil::checkString($var, True);
        $this->scheme = $var;

        return $this;
    }

    /**
     * The HTTP URL query in the format of `name1=value1&name2=value2`, as it
     * appears in the first line of the HTTP request. No decoding is performed.
     *
     * Generated from protobuf field <code>string query = 7;</code>
     * @return string
     */
    public function getQuery()
    {
        return $this->query;
    }

    /**
     * The HTTP URL query in the format of `name1=value1&name2=value2`, as it
     * appears in the first line of the HTTP request. No decoding is performed.
     *
     * Generated from protobuf field <code>string query = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setQuery($var)
    {
        GPBUtil::checkString($var, True);
        $this->query = $var;

        return $this;
    }

    /**
     * The timestamp when the `destination` service receives the last byte of
     * the request.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp time = 9;</code>
     * @return \Google\Protobuf\Timestamp|null
     */
    public function getTime()
    {
        return $this->time;
    }

    public function hasTime()
    {
        return isset($this->time);
    }

    public function clearTime()
    {
        unset($this->time);
    }

    /**
     * The timestamp when the `destination` service receives the last byte of
     * the request.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp time = 9;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->time = $var;

        return $this;
    }

    /**
     * The HTTP request size in bytes. If unknown, it must be -1.
     *
     * Generated from protobuf field <code>int64 size = 10;</code>
     * @return int|string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * The HTTP request size in bytes. If unknown, it must be -1.
     *
     * Generated from protobuf field <code>int64 size = 10;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSize($var)
    {
        GPBUtil::checkInt64($var);
        $this->size = $var;

        return $this;
    }

    /**
     * The network protocol used with the request, such as "http/1.1",
     * "spdy/3", "h2", "h2c", "webrtc", "tcp", "udp", "quic". See
     * https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids
     * for details.
     *
     * Generated from protobuf field <code>string protocol = 11;</code>
     * @return string
     */
    public function getProtocol()
    {
        return $this->protocol;
    }

    /**
     * The network protocol used with the request, such as "http/1.1",
     * "spdy/3", "h2", "h2c", "webrtc", "tcp", "udp", "quic". See
     * https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids
     * for details.
     *
     * Generated from protobuf field <code>string protocol = 11;</code>
     * @param string $var
     * @return $this
     */
    public function setProtocol($var)
    {
        GPBUtil::checkString($var, True);
        $this->protocol = $var;

        return $this;
    }

    /**
     * A special parameter for request reason. It is used by security systems
     * to associate auditing information with a request.
     *
     * Generated from protobuf field <code>string reason = 12;</code>
     * @return string
     */
    public function getReason()
    {
        return $this->reason;
    }

    /**
     * A special parameter for request reason. It is used by security systems
     * to associate auditing information with a request.
     *
     * Generated from protobuf field <code>string reason = 12;</code>
     * @param string $var
     * @return $this
     */
    public function setReason($var)
    {
        GPBUtil::checkString($var, True);
        $this->reason = $var;

        return $this;
    }

    /**
     * The request authentication. May be absent for unauthenticated requests.
     * Derived from the HTTP request `Authorization` header or equivalent.
     *
     * Generated from protobuf field <code>.google.rpc.context.AttributeContext.Auth auth = 13;</code>
     * @return \Google\Rpc\Context\AttributeContext\Auth|null
     */
    public function getAuth()
    {
        return $this->auth;
    }

    public function hasAuth()
    {
        return isset($this->auth);
    }

    public function clearAuth()
    {
        unset($this->auth);
    }

    /**
     * The request authentication. May be absent for unauthenticated requests.
     * Derived from the HTTP request `Authorization` header or equivalent.
     *
     * Generated from protobuf field <code>.google.rpc.context.AttributeContext.Auth auth = 13;</code>
     * @param \Google\Rpc\Context\AttributeContext\Auth $var
     * @return $this
     */
    public function setAuth($var)
    {
        GPBUtil::checkMessage($var, \Google\Rpc\Context\AttributeContext\Auth::class);
        $this->auth = $var;

        return $this;
    }

}


