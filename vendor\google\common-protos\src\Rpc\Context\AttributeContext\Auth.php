<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/context/attribute_context.proto

namespace Google\Rpc\Context\AttributeContext;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * This message defines request authentication attributes. Terminology is
 * based on the JSON Web Token (JWT) standard, but the terms also
 * correlate to concepts in other standards.
 *
 * Generated from protobuf message <code>google.rpc.context.AttributeContext.Auth</code>
 */
class Auth extends \Google\Protobuf\Internal\Message
{
    /**
     * The authenticated principal. Reflects the issuer (`iss`) and subject
     * (`sub`) claims within a JWT. The issuer and subject should be `/`
     * delimited, with `/` percent-encoded within the subject fragment. For
     * Google accounts, the principal format is:
     * "https://accounts.google.com/{id}"
     *
     * Generated from protobuf field <code>string principal = 1;</code>
     */
    protected $principal = '';
    /**
     * The intended audience(s) for this authentication information. Reflects
     * the audience (`aud`) claim within a JWT. The audience
     * value(s) depends on the `issuer`, but typically include one or more of
     * the following pieces of information:
     * *  The services intended to receive the credential. For example,
     *    ["https://pubsub.googleapis.com/", "https://storage.googleapis.com/"].
     * *  A set of service-based scopes. For example,
     *    ["https://www.googleapis.com/auth/cloud-platform"].
     * *  The client id of an app, such as the Firebase project id for JWTs
     *    from Firebase Auth.
     * Consult the documentation for the credential issuer to determine the
     * information provided.
     *
     * Generated from protobuf field <code>repeated string audiences = 2;</code>
     */
    private $audiences;
    /**
     * The authorized presenter of the credential. Reflects the optional
     * Authorized Presenter (`azp`) claim within a JWT or the
     * OAuth client id. For example, a Google Cloud Platform client id looks
     * as follows: "************.apps.googleusercontent.com".
     *
     * Generated from protobuf field <code>string presenter = 3;</code>
     */
    protected $presenter = '';
    /**
     * Structured claims presented with the credential. JWTs include
     * `{key: value}` pairs for standard and private claims. The following
     * is a subset of the standard required and optional claims that would
     * typically be presented for a Google-based JWT:
     *    {'iss': 'accounts.google.com',
     *     'sub': '113289723416554971153',
     *     'aud': ['************', 'pubsub.googleapis.com'],
     *     'azp': '************.apps.googleusercontent.com',
     *     'email': 'jsmith&#64;example.com',
     *     'iat': **********,
     *     'exp': **********}
     * SAML assertions are similarly specified, but with an identity provider
     * dependent structure.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct claims = 4;</code>
     */
    protected $claims = null;
    /**
     * A list of access level resource names that allow resources to be
     * accessed by authenticated requester. It is part of Secure GCP processing
     * for the incoming request. An access level string has the format:
     * "//{api_service_name}/accessPolicies/{policy_id}/accessLevels/{short_name}"
     * Example:
     * "//accesscontextmanager.googleapis.com/accessPolicies/MY_POLICY_ID/accessLevels/MY_LEVEL"
     *
     * Generated from protobuf field <code>repeated string access_levels = 5;</code>
     */
    private $access_levels;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $principal
     *           The authenticated principal. Reflects the issuer (`iss`) and subject
     *           (`sub`) claims within a JWT. The issuer and subject should be `/`
     *           delimited, with `/` percent-encoded within the subject fragment. For
     *           Google accounts, the principal format is:
     *           "https://accounts.google.com/{id}"
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $audiences
     *           The intended audience(s) for this authentication information. Reflects
     *           the audience (`aud`) claim within a JWT. The audience
     *           value(s) depends on the `issuer`, but typically include one or more of
     *           the following pieces of information:
     *           *  The services intended to receive the credential. For example,
     *              ["https://pubsub.googleapis.com/", "https://storage.googleapis.com/"].
     *           *  A set of service-based scopes. For example,
     *              ["https://www.googleapis.com/auth/cloud-platform"].
     *           *  The client id of an app, such as the Firebase project id for JWTs
     *              from Firebase Auth.
     *           Consult the documentation for the credential issuer to determine the
     *           information provided.
     *     @type string $presenter
     *           The authorized presenter of the credential. Reflects the optional
     *           Authorized Presenter (`azp`) claim within a JWT or the
     *           OAuth client id. For example, a Google Cloud Platform client id looks
     *           as follows: "************.apps.googleusercontent.com".
     *     @type \Google\Protobuf\Struct $claims
     *           Structured claims presented with the credential. JWTs include
     *           `{key: value}` pairs for standard and private claims. The following
     *           is a subset of the standard required and optional claims that would
     *           typically be presented for a Google-based JWT:
     *              {'iss': 'accounts.google.com',
     *               'sub': '113289723416554971153',
     *               'aud': ['************', 'pubsub.googleapis.com'],
     *               'azp': '************.apps.googleusercontent.com',
     *               'email': 'jsmith&#64;example.com',
     *               'iat': **********,
     *               'exp': **********}
     *           SAML assertions are similarly specified, but with an identity provider
     *           dependent structure.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $access_levels
     *           A list of access level resource names that allow resources to be
     *           accessed by authenticated requester. It is part of Secure GCP processing
     *           for the incoming request. An access level string has the format:
     *           "//{api_service_name}/accessPolicies/{policy_id}/accessLevels/{short_name}"
     *           Example:
     *           "//accesscontextmanager.googleapis.com/accessPolicies/MY_POLICY_ID/accessLevels/MY_LEVEL"
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Rpc\Context\AttributeContext::initOnce();
        parent::__construct($data);
    }

    /**
     * The authenticated principal. Reflects the issuer (`iss`) and subject
     * (`sub`) claims within a JWT. The issuer and subject should be `/`
     * delimited, with `/` percent-encoded within the subject fragment. For
     * Google accounts, the principal format is:
     * "https://accounts.google.com/{id}"
     *
     * Generated from protobuf field <code>string principal = 1;</code>
     * @return string
     */
    public function getPrincipal()
    {
        return $this->principal;
    }

    /**
     * The authenticated principal. Reflects the issuer (`iss`) and subject
     * (`sub`) claims within a JWT. The issuer and subject should be `/`
     * delimited, with `/` percent-encoded within the subject fragment. For
     * Google accounts, the principal format is:
     * "https://accounts.google.com/{id}"
     *
     * Generated from protobuf field <code>string principal = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPrincipal($var)
    {
        GPBUtil::checkString($var, True);
        $this->principal = $var;

        return $this;
    }

    /**
     * The intended audience(s) for this authentication information. Reflects
     * the audience (`aud`) claim within a JWT. The audience
     * value(s) depends on the `issuer`, but typically include one or more of
     * the following pieces of information:
     * *  The services intended to receive the credential. For example,
     *    ["https://pubsub.googleapis.com/", "https://storage.googleapis.com/"].
     * *  A set of service-based scopes. For example,
     *    ["https://www.googleapis.com/auth/cloud-platform"].
     * *  The client id of an app, such as the Firebase project id for JWTs
     *    from Firebase Auth.
     * Consult the documentation for the credential issuer to determine the
     * information provided.
     *
     * Generated from protobuf field <code>repeated string audiences = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAudiences()
    {
        return $this->audiences;
    }

    /**
     * The intended audience(s) for this authentication information. Reflects
     * the audience (`aud`) claim within a JWT. The audience
     * value(s) depends on the `issuer`, but typically include one or more of
     * the following pieces of information:
     * *  The services intended to receive the credential. For example,
     *    ["https://pubsub.googleapis.com/", "https://storage.googleapis.com/"].
     * *  A set of service-based scopes. For example,
     *    ["https://www.googleapis.com/auth/cloud-platform"].
     * *  The client id of an app, such as the Firebase project id for JWTs
     *    from Firebase Auth.
     * Consult the documentation for the credential issuer to determine the
     * information provided.
     *
     * Generated from protobuf field <code>repeated string audiences = 2;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAudiences($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->audiences = $arr;

        return $this;
    }

    /**
     * The authorized presenter of the credential. Reflects the optional
     * Authorized Presenter (`azp`) claim within a JWT or the
     * OAuth client id. For example, a Google Cloud Platform client id looks
     * as follows: "************.apps.googleusercontent.com".
     *
     * Generated from protobuf field <code>string presenter = 3;</code>
     * @return string
     */
    public function getPresenter()
    {
        return $this->presenter;
    }

    /**
     * The authorized presenter of the credential. Reflects the optional
     * Authorized Presenter (`azp`) claim within a JWT or the
     * OAuth client id. For example, a Google Cloud Platform client id looks
     * as follows: "************.apps.googleusercontent.com".
     *
     * Generated from protobuf field <code>string presenter = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setPresenter($var)
    {
        GPBUtil::checkString($var, True);
        $this->presenter = $var;

        return $this;
    }

    /**
     * Structured claims presented with the credential. JWTs include
     * `{key: value}` pairs for standard and private claims. The following
     * is a subset of the standard required and optional claims that would
     * typically be presented for a Google-based JWT:
     *    {'iss': 'accounts.google.com',
     *     'sub': '113289723416554971153',
     *     'aud': ['************', 'pubsub.googleapis.com'],
     *     'azp': '************.apps.googleusercontent.com',
     *     'email': 'jsmith&#64;example.com',
     *     'iat': **********,
     *     'exp': **********}
     * SAML assertions are similarly specified, but with an identity provider
     * dependent structure.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct claims = 4;</code>
     * @return \Google\Protobuf\Struct|null
     */
    public function getClaims()
    {
        return $this->claims;
    }

    public function hasClaims()
    {
        return isset($this->claims);
    }

    public function clearClaims()
    {
        unset($this->claims);
    }

    /**
     * Structured claims presented with the credential. JWTs include
     * `{key: value}` pairs for standard and private claims. The following
     * is a subset of the standard required and optional claims that would
     * typically be presented for a Google-based JWT:
     *    {'iss': 'accounts.google.com',
     *     'sub': '113289723416554971153',
     *     'aud': ['************', 'pubsub.googleapis.com'],
     *     'azp': '************.apps.googleusercontent.com',
     *     'email': 'jsmith&#64;example.com',
     *     'iat': **********,
     *     'exp': **********}
     * SAML assertions are similarly specified, but with an identity provider
     * dependent structure.
     *
     * Generated from protobuf field <code>.google.protobuf.Struct claims = 4;</code>
     * @param \Google\Protobuf\Struct $var
     * @return $this
     */
    public function setClaims($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Struct::class);
        $this->claims = $var;

        return $this;
    }

    /**
     * A list of access level resource names that allow resources to be
     * accessed by authenticated requester. It is part of Secure GCP processing
     * for the incoming request. An access level string has the format:
     * "//{api_service_name}/accessPolicies/{policy_id}/accessLevels/{short_name}"
     * Example:
     * "//accesscontextmanager.googleapis.com/accessPolicies/MY_POLICY_ID/accessLevels/MY_LEVEL"
     *
     * Generated from protobuf field <code>repeated string access_levels = 5;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAccessLevels()
    {
        return $this->access_levels;
    }

    /**
     * A list of access level resource names that allow resources to be
     * accessed by authenticated requester. It is part of Secure GCP processing
     * for the incoming request. An access level string has the format:
     * "//{api_service_name}/accessPolicies/{policy_id}/accessLevels/{short_name}"
     * Example:
     * "//accesscontextmanager.googleapis.com/accessPolicies/MY_POLICY_ID/accessLevels/MY_LEVEL"
     *
     * Generated from protobuf field <code>repeated string access_levels = 5;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAccessLevels($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->access_levels = $arr;

        return $this;
    }

}


