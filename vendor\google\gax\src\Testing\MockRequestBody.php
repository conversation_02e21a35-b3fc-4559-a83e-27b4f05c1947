<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Testing/mocks.proto

namespace Google\ApiCore\Testing;

use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>google.apicore.testing.MockRequestBody</code>
 *
 * @internal
 */
class MockRequestBody extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string name = 1;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>uint64 number = 2;</code>
     */
    protected $number = 0;
    /**
     * Generated from protobuf field <code>repeated string repeated_field = 3;</code>
     */
    private $repeated_field;
    /**
     * Generated from protobuf field <code>.google.apicore.testing.MockRequestBody nested_message = 4;</code>
     */
    protected $nested_message = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.BytesValue bytes_value = 5;</code>
     */
    protected $bytes_value = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.Duration duration_value = 6;</code>
     */
    protected $duration_value = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.FieldMask field_mask = 7;</code>
     */
    protected $field_mask = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.Int64Value int64_value = 8;</code>
     */
    protected $int64_value = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.ListValue list_value = 9;</code>
     */
    protected $list_value = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.StringValue string_value = 10;</code>
     */
    protected $string_value = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.Struct struct_value = 11;</code>
     */
    protected $struct_value = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.Timestamp timestamp_value = 12;</code>
     */
    protected $timestamp_value = null;
    /**
     * Generated from protobuf field <code>.google.protobuf.Value value_value = 13;</code>
     */
    protected $value_value = null;
    protected $oneof_field;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $name
     *     @type int|string $number
     *     @type string[]|\Google\Protobuf\Internal\RepeatedField $repeated_field
     *     @type \Google\ApiCore\Testing\MockRequestBody $nested_message
     *     @type \Google\Protobuf\BytesValue $bytes_value
     *     @type \Google\Protobuf\Duration $duration_value
     *     @type \Google\Protobuf\FieldMask $field_mask
     *     @type \Google\Protobuf\Int64Value $int64_value
     *     @type \Google\Protobuf\ListValue $list_value
     *     @type \Google\Protobuf\StringValue $string_value
     *     @type \Google\Protobuf\Struct $struct_value
     *     @type \Google\Protobuf\Timestamp $timestamp_value
     *     @type \Google\Protobuf\Value $value_value
     *     @type string $field_1
     *     @type string $field_2
     *     @type string $field_3
     * }
     */
    public function __construct($data = null)
    {
        \GPBMetadata\ApiCore\Testing\Mocks::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string name = 1;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, true);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 number = 2;</code>
     * @return int|string
     */
    public function getNumber()
    {
        return $this->number;
    }

    /**
     * Generated from protobuf field <code>uint64 number = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setNumber($var)
    {
        GPBUtil::checkUint64($var);
        $this->number = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated string repeated_field = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getRepeatedField()
    {
        return $this->repeated_field;
    }

    /**
     * Generated from protobuf field <code>repeated string repeated_field = 3;</code>
     * @param string[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setRepeatedField($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->repeated_field = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.apicore.testing.MockRequestBody nested_message = 4;</code>
     * @return \Google\ApiCore\Testing\MockRequestBody
     */
    public function getNestedMessage()
    {
        return isset($this->nested_message) ? $this->nested_message : null;
    }

    public function hasNestedMessage()
    {
        return isset($this->nested_message);
    }

    public function clearNestedMessage()
    {
        unset($this->nested_message);
    }

    /**
     * Generated from protobuf field <code>.google.apicore.testing.MockRequestBody nested_message = 4;</code>
     * @param \Google\ApiCore\Testing\MockRequestBody $var
     * @return $this
     */
    public function setNestedMessage($var)
    {
        GPBUtil::checkMessage($var, \Google\ApiCore\Testing\MockRequestBody::class);
        $this->nested_message = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.BytesValue bytes_value = 5;</code>
     * @return \Google\Protobuf\BytesValue
     */
    public function getBytesValue()
    {
        return isset($this->bytes_value) ? $this->bytes_value : null;
    }

    public function hasBytesValue()
    {
        return isset($this->bytes_value);
    }

    public function clearBytesValue()
    {
        unset($this->bytes_value);
    }

    /**
     * Returns the unboxed value from <code>getBytesValue()</code>

     * Generated from protobuf field <code>.google.protobuf.BytesValue bytes_value = 5;</code>
     * @return string|null
     */
    public function getBytesValueUnwrapped()
    {
        return $this->readWrapperValue('bytes_value');
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.BytesValue bytes_value = 5;</code>
     * @param \Google\Protobuf\BytesValue $var
     * @return $this
     */
    public function setBytesValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\BytesValue::class);
        $this->bytes_value = $var;

        return $this;
    }

    /**
     * Sets the field by wrapping a primitive type in a Google\Protobuf\BytesValue object.

     * Generated from protobuf field <code>.google.protobuf.BytesValue bytes_value = 5;</code>
     * @param string|null $var
     * @return $this
     */
    public function setBytesValueUnwrapped($var)
    {
        $this->writeWrapperValue('bytes_value', $var);
        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Duration duration_value = 6;</code>
     * @return \Google\Protobuf\Duration
     */
    public function getDurationValue()
    {
        return isset($this->duration_value) ? $this->duration_value : null;
    }

    public function hasDurationValue()
    {
        return isset($this->duration_value);
    }

    public function clearDurationValue()
    {
        unset($this->duration_value);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Duration duration_value = 6;</code>
     * @param \Google\Protobuf\Duration $var
     * @return $this
     */
    public function setDurationValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Duration::class);
        $this->duration_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.FieldMask field_mask = 7;</code>
     * @return \Google\Protobuf\FieldMask
     */
    public function getFieldMask()
    {
        return isset($this->field_mask) ? $this->field_mask : null;
    }

    public function hasFieldMask()
    {
        return isset($this->field_mask);
    }

    public function clearFieldMask()
    {
        unset($this->field_mask);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.FieldMask field_mask = 7;</code>
     * @param \Google\Protobuf\FieldMask $var
     * @return $this
     */
    public function setFieldMask($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\FieldMask::class);
        $this->field_mask = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Int64Value int64_value = 8;</code>
     * @return \Google\Protobuf\Int64Value
     */
    public function getInt64Value()
    {
        return isset($this->int64_value) ? $this->int64_value : null;
    }

    public function hasInt64Value()
    {
        return isset($this->int64_value);
    }

    public function clearInt64Value()
    {
        unset($this->int64_value);
    }

    /**
     * Returns the unboxed value from <code>getInt64Value()</code>

     * Generated from protobuf field <code>.google.protobuf.Int64Value int64_value = 8;</code>
     * @return int|string|null
     */
    public function getInt64ValueUnwrapped()
    {
        return $this->readWrapperValue('int64_value');
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Int64Value int64_value = 8;</code>
     * @param \Google\Protobuf\Int64Value $var
     * @return $this
     */
    public function setInt64Value($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Int64Value::class);
        $this->int64_value = $var;

        return $this;
    }

    /**
     * Sets the field by wrapping a primitive type in a Google\Protobuf\Int64Value object.

     * Generated from protobuf field <code>.google.protobuf.Int64Value int64_value = 8;</code>
     * @param int|string|null $var
     * @return $this
     */
    public function setInt64ValueUnwrapped($var)
    {
        $this->writeWrapperValue('int64_value', $var);
        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.ListValue list_value = 9;</code>
     * @return \Google\Protobuf\ListValue
     */
    public function getListValue()
    {
        return isset($this->list_value) ? $this->list_value : null;
    }

    public function hasListValue()
    {
        return isset($this->list_value);
    }

    public function clearListValue()
    {
        unset($this->list_value);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.ListValue list_value = 9;</code>
     * @param \Google\Protobuf\ListValue $var
     * @return $this
     */
    public function setListValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\ListValue::class);
        $this->list_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.StringValue string_value = 10;</code>
     * @return \Google\Protobuf\StringValue
     */
    public function getStringValue()
    {
        return isset($this->string_value) ? $this->string_value : null;
    }

    public function hasStringValue()
    {
        return isset($this->string_value);
    }

    public function clearStringValue()
    {
        unset($this->string_value);
    }

    /**
     * Returns the unboxed value from <code>getStringValue()</code>

     * Generated from protobuf field <code>.google.protobuf.StringValue string_value = 10;</code>
     * @return string|null
     */
    public function getStringValueUnwrapped()
    {
        return $this->readWrapperValue('string_value');
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.StringValue string_value = 10;</code>
     * @param \Google\Protobuf\StringValue $var
     * @return $this
     */
    public function setStringValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\StringValue::class);
        $this->string_value = $var;

        return $this;
    }

    /**
     * Sets the field by wrapping a primitive type in a Google\Protobuf\StringValue object.

     * Generated from protobuf field <code>.google.protobuf.StringValue string_value = 10;</code>
     * @param string|null $var
     * @return $this
     */
    public function setStringValueUnwrapped($var)
    {
        $this->writeWrapperValue('string_value', $var);
        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Struct struct_value = 11;</code>
     * @return \Google\Protobuf\Struct
     */
    public function getStructValue()
    {
        return isset($this->struct_value) ? $this->struct_value : null;
    }

    public function hasStructValue()
    {
        return isset($this->struct_value);
    }

    public function clearStructValue()
    {
        unset($this->struct_value);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Struct struct_value = 11;</code>
     * @param \Google\Protobuf\Struct $var
     * @return $this
     */
    public function setStructValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Struct::class);
        $this->struct_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Timestamp timestamp_value = 12;</code>
     * @return \Google\Protobuf\Timestamp
     */
    public function getTimestampValue()
    {
        return isset($this->timestamp_value) ? $this->timestamp_value : null;
    }

    public function hasTimestampValue()
    {
        return isset($this->timestamp_value);
    }

    public function clearTimestampValue()
    {
        unset($this->timestamp_value);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Timestamp timestamp_value = 12;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setTimestampValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->timestamp_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Value value_value = 13;</code>
     * @return \Google\Protobuf\Value
     */
    public function getValueValue()
    {
        return isset($this->value_value) ? $this->value_value : null;
    }

    public function hasValueValue()
    {
        return isset($this->value_value);
    }

    public function clearValueValue()
    {
        unset($this->value_value);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Value value_value = 13;</code>
     * @param \Google\Protobuf\Value $var
     * @return $this
     */
    public function setValueValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Value::class);
        $this->value_value = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string field_1 = 14;</code>
     * @return string
     */
    public function getField1()
    {
        return $this->readOneof(14);
    }

    public function hasField1()
    {
        return $this->hasOneof(14);
    }

    /**
     * Generated from protobuf field <code>string field_1 = 14;</code>
     * @param string $var
     * @return $this
     */
    public function setField1($var)
    {
        GPBUtil::checkString($var, true);
        $this->writeOneof(14, $var);

        return $this;
    }

    /**
     * Generated from protobuf field <code>string field_2 = 15;</code>
     * @return string
     */
    public function getField2()
    {
        return $this->readOneof(15);
    }

    public function hasField2()
    {
        return $this->hasOneof(15);
    }

    /**
     * Generated from protobuf field <code>string field_2 = 15;</code>
     * @param string $var
     * @return $this
     */
    public function setField2($var)
    {
        GPBUtil::checkString($var, true);
        $this->writeOneof(15, $var);

        return $this;
    }

    /**
     * Generated from protobuf field <code>string field_3 = 16;</code>
     * @return string
     */
    public function getField3()
    {
        return $this->readOneof(16);
    }

    public function hasField3()
    {
        return $this->hasOneof(16);
    }

    /**
     * Generated from protobuf field <code>string field_3 = 16;</code>
     * @param string $var
     * @return $this
     */
    public function setField3($var)
    {
        GPBUtil::checkString($var, true);
        $this->writeOneof(16, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getOneofField()
    {
        return $this->whichOneof('oneof_field');
    }

}
