<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/context/attribute_context.proto

namespace Google\Rpc\Context\AttributeContext;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * This message defines attributes for a node that handles a network request.
 * The node can be either a service or an application that sends, forwards,
 * or receives the request. Service peers should fill in
 * `principal` and `labels` as appropriate.
 *
 * Generated from protobuf message <code>google.rpc.context.AttributeContext.Peer</code>
 */
class Peer extends \Google\Protobuf\Internal\Message
{
    /**
     * The IP address of the peer.
     *
     * Generated from protobuf field <code>string ip = 1;</code>
     */
    protected $ip = '';
    /**
     * The network port of the peer.
     *
     * Generated from protobuf field <code>int64 port = 2;</code>
     */
    protected $port = 0;
    /**
     * The labels associated with the peer.
     *
     * Generated from protobuf field <code>map<string, string> labels = 6;</code>
     */
    private $labels;
    /**
     * The identity of this peer. Similar to `Request.auth.principal`, but
     * relative to the peer instead of the request. For example, the
     * identity associated with a load balancer that forwarded the request.
     *
     * Generated from protobuf field <code>string principal = 7;</code>
     */
    protected $principal = '';
    /**
     * The CLDR country/region code associated with the above IP address.
     * If the IP address is private, the `region_code` should reflect the
     * physical location where this peer is running.
     *
     * Generated from protobuf field <code>string region_code = 8;</code>
     */
    protected $region_code = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $ip
     *           The IP address of the peer.
     *     @type int|string $port
     *           The network port of the peer.
     *     @type array|\Google\Protobuf\Internal\MapField $labels
     *           The labels associated with the peer.
     *     @type string $principal
     *           The identity of this peer. Similar to `Request.auth.principal`, but
     *           relative to the peer instead of the request. For example, the
     *           identity associated with a load balancer that forwarded the request.
     *     @type string $region_code
     *           The CLDR country/region code associated with the above IP address.
     *           If the IP address is private, the `region_code` should reflect the
     *           physical location where this peer is running.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Rpc\Context\AttributeContext::initOnce();
        parent::__construct($data);
    }

    /**
     * The IP address of the peer.
     *
     * Generated from protobuf field <code>string ip = 1;</code>
     * @return string
     */
    public function getIp()
    {
        return $this->ip;
    }

    /**
     * The IP address of the peer.
     *
     * Generated from protobuf field <code>string ip = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setIp($var)
    {
        GPBUtil::checkString($var, True);
        $this->ip = $var;

        return $this;
    }

    /**
     * The network port of the peer.
     *
     * Generated from protobuf field <code>int64 port = 2;</code>
     * @return int|string
     */
    public function getPort()
    {
        return $this->port;
    }

    /**
     * The network port of the peer.
     *
     * Generated from protobuf field <code>int64 port = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPort($var)
    {
        GPBUtil::checkInt64($var);
        $this->port = $var;

        return $this;
    }

    /**
     * The labels associated with the peer.
     *
     * Generated from protobuf field <code>map<string, string> labels = 6;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getLabels()
    {
        return $this->labels;
    }

    /**
     * The labels associated with the peer.
     *
     * Generated from protobuf field <code>map<string, string> labels = 6;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setLabels($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->labels = $arr;

        return $this;
    }

    /**
     * The identity of this peer. Similar to `Request.auth.principal`, but
     * relative to the peer instead of the request. For example, the
     * identity associated with a load balancer that forwarded the request.
     *
     * Generated from protobuf field <code>string principal = 7;</code>
     * @return string
     */
    public function getPrincipal()
    {
        return $this->principal;
    }

    /**
     * The identity of this peer. Similar to `Request.auth.principal`, but
     * relative to the peer instead of the request. For example, the
     * identity associated with a load balancer that forwarded the request.
     *
     * Generated from protobuf field <code>string principal = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setPrincipal($var)
    {
        GPBUtil::checkString($var, True);
        $this->principal = $var;

        return $this;
    }

    /**
     * The CLDR country/region code associated with the above IP address.
     * If the IP address is private, the `region_code` should reflect the
     * physical location where this peer is running.
     *
     * Generated from protobuf field <code>string region_code = 8;</code>
     * @return string
     */
    public function getRegionCode()
    {
        return $this->region_code;
    }

    /**
     * The CLDR country/region code associated with the above IP address.
     * If the IP address is private, the `region_code` should reflect the
     * physical location where this peer is running.
     *
     * Generated from protobuf field <code>string region_code = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setRegionCode($var)
    {
        GPBUtil::checkString($var, True);
        $this->region_code = $var;

        return $this;
    }

}


