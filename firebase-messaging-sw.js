// Use importScripts instead of import statements
importScripts('https://www.gstatic.com/firebasejs/11.5.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging-compat.js');

// PWA Configuration
const CACHE_NAME = 'q-pwa-cache-v1';
const OFFLINE_URL = '/offline.html';
const CACHE_STRATEGY = 'CACHE_STRATEGY_PLACEHOLDER'; // Will be replaced during build

// Define what to cache
const STATIC_CACHE_URLS = [
    '/',
    '/wp-content/themes/',
    '/wp-content/plugins/q-pusher-q-pwa/includes/css/pwa-styles.css',
    '/wp-content/plugins/q-pusher-q-pwa/includes/js/pwa-manager.js',
    '/manifest.json',
    '/offline.html'
];

const CACHE_STRATEGIES = {
    CACHE_FIRST: 'cache_first',
    NETWORK_FIRST: 'network_first',
    STALE_WHILE_REVALIDATE: 'stale_while_revalidate'
};

// Initialize Firebase with configuration details and error handling
try {
    firebase.initializeApp({
        apiKey: "FIREBASE_API_KEY",
        authDomain: "FIREBASE_AUTH_DOMAIN",
        projectId: "FIREBASE_PROJECT_ID",
        storageBucket: "FIREBASE_STORAGE_BUCKET",
        messagingSenderId: "FIREBASE_MESSAGING_SENDER_ID",
        appId: "FIREBASE_APP_ID"
    });
    const messaging = firebase.messaging();

    // Enhanced notification tracking with better validation
    const notificationCache = {
        messages: new Map(),
        cleanupInterval: 60000, // 60 seconds
        maxAge: 60000, // 60 seconds

        addMessage(key, timestamp) {
            if (!key) return false;
            this.cleanup();
            this.messages.set(key, timestamp);
            return true;
        },

        hasMessage(key) {
            if (!key) return false;
            this.cleanup();
            return this.messages.has(key);
        },

        cleanup() {
            const now = Date.now();
            let cleaned = 0;
            for (const [key, timestamp] of this.messages.entries()) {
                if (now - timestamp > this.maxAge) {
                    this.messages.delete(key);
                    cleaned++;
                }
            }
            if (cleaned > 0) {
                console.log(`Cleaned ${cleaned} old notifications`);
            }
        }
    };

    // Handle background messages with improved error handling and prevent duplicate notifications
    messaging.onBackgroundMessage(async (payload) => {
        try {
            console.log('Processing background message:', payload);

            // Handle data-only messages
            const { data } = payload;
            if (!data?.title) {
                console.error('Invalid notification payload');
                return;
            }

            const timestamp = parseInt(data.timestamp || Date.now() / 1000) * 1000;
            const now = Date.now();
            const notificationKey = data.message_id || `${data.title}-${timestamp}`;

            if (notificationCache.hasMessage(notificationKey)) {
                console.log('Duplicate notification filtered:', notificationKey);
                return;
            }

            if (now - timestamp > notificationCache.maxAge) {
                console.log('Discarding outdated notification:', notificationKey);
                return;
            }

            notificationCache.addMessage(notificationKey, now);

            // Create and show the notification
            const notificationOptions = {
                title: data.title,
                body: data.body,
                icon: data.icon || '/favicon.ico',
                data: {
                    url: data.click_action || self.registration.scope,
                    notification_id: data.notification_id || '',
                    form_id: data.form_id || ''
                },
                requireInteraction: true,
                tag: data.message_id || `notification-${Date.now()}`,
                click_action: data.click_action || self.registration.scope,
                badge: data.badge || data.icon || '/favicon.ico'
            };

            // Only add image if it exists and is a valid URL
            if (data.image && data.image.startsWith('http')) {
                notificationOptions.image = data.image;
            }

            // Show the notification
            await self.registration.showNotification(
                notificationOptions.title,
                notificationOptions
            );

        } catch (error) {
            console.error('Error processing notification:', error);
        }
    });

    // Notification click handler with improved navigation
    self.addEventListener('notificationclick', event => {
        try {
            event.notification.close();

            // Get URL from notification data or fallback to scope
            let navigateUrl = event.notification.data?.url || self.registration.scope;

            // Ensure URL is absolute
            if (!navigateUrl.startsWith('http')) {
                navigateUrl = self.registration.scope + navigateUrl.replace(/^\//, '');
            }

            // Notify client to clear badge count
            self.clients.matchAll().then(clients => {
                clients.forEach(client => {
                    client.postMessage({
                        type: 'NOTIFICATION_CLICKED',
                        notification: {
                            id: event.notification.data?.notification_id || '',
                            title: event.notification.title
                        }
                    });
                });
            });

            // Track notification click with form_id
            const notificationData = event.notification.data || {};
            if (notificationData.notification_id) {
                fetch('/wp-admin/admin-ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'q_track_notification_click',
                        notification_id: notificationData.notification_id,
                        form_id: notificationData.form_id || ''
                    })
                }).catch(console.error);
            }

            // Focus existing window or open new one
            event.waitUntil(
                clients.matchAll({
                    type: 'window',
                    includeUncontrolled: true
                })
                .then(clientList => {
                    // Try to find an existing tab with the same origin
                    const matchingClient = clientList.find(client => {
                        const clientUrl = new URL(client.url);
                        const targetUrl = new URL(navigateUrl);
                        return clientUrl.origin === targetUrl.origin;
                    });

                    if (matchingClient) {
                        // If we found a matching tab, focus it and navigate
                        return matchingClient.focus().then(client => {
                            // Only navigate if URLs are different
                            if (client.url !== navigateUrl) {
                                return client.navigate(navigateUrl);
                            }
                        });
                    }

                    // If no existing tab found, check if we have any tab from our origin
                    const anyClientFromOrigin = clientList.find(client => {
                        const clientUrl = new URL(client.url);
                        const targetUrl = new URL(navigateUrl);
                        return clientUrl.origin === targetUrl.origin;
                    });

                    if (anyClientFromOrigin) {
                        // Reuse existing tab from same origin
                        return anyClientFromOrigin.focus().then(client => {
                            return client.navigate(navigateUrl);
                        });
                    }

                    // If no existing tabs found, open a new one
                    return clients.openWindow(navigateUrl);
                })
            );
        } catch (error) {
            console.error('Error handling notification click:', error);
            // Fallback to simple window open
            event.waitUntil(clients.openWindow(self.registration.scope));
        }
    });

    // Automated cleanup
    setInterval(() => notificationCache.cleanup(), notificationCache.cleanupInterval);

} catch (error) {
    console.error('Firebase initialization failed:', error);
}

// PWA Service Worker Functionality

// Install event - cache static resources
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching static resources');
                return cache.addAll(STATIC_CACHE_URLS.filter(url => url));
            })
            .then(() => {
                console.log('Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');

    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle network requests with caching strategies
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip Chrome extension requests
    if (event.request.url.startsWith('chrome-extension://')) {
        return;
    }

    // Skip admin and login pages
    if (event.request.url.includes('/wp-admin/') ||
        event.request.url.includes('/wp-login.php')) {
        return;
    }

    event.respondWith(handleFetchRequest(event.request));
});

// Handle fetch requests based on cache strategy
async function handleFetchRequest(request) {
    const url = new URL(request.url);

    try {
        // Determine cache strategy based on resource type
        let strategy = CACHE_STRATEGY;

        // Override strategy for specific resource types
        if (isStaticResource(url)) {
            strategy = CACHE_STRATEGIES.CACHE_FIRST;
        } else if (isAPIRequest(url)) {
            strategy = CACHE_STRATEGIES.NETWORK_FIRST;
        } else if (isPageRequest(url)) {
            strategy = CACHE_STRATEGIES.STALE_WHILE_REVALIDATE;
        }

        switch (strategy) {
            case CACHE_STRATEGIES.CACHE_FIRST:
                return await cacheFirst(request);
            case CACHE_STRATEGIES.NETWORK_FIRST:
                return await networkFirst(request);
            case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
                return await staleWhileRevalidate(request);
            default:
                return await networkFirst(request);
        }
    } catch (error) {
        console.error('Service Worker: Fetch error', error);
        return await handleOfflineRequest(request);
    }
}

// Cache First Strategy
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);

    if (cachedResponse) {
        return cachedResponse;
    }

    try {
        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        return await handleOfflineRequest(request);
    }
}

// Network First Strategy
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        const cachedResponse = await caches.match(request);

        if (cachedResponse) {
            return cachedResponse;
        }

        return await handleOfflineRequest(request);
    }
}

// Stale While Revalidate Strategy
async function staleWhileRevalidate(request) {
    const cachedResponse = await caches.match(request);

    const fetchPromise = fetch(request).then(async (networkResponse) => {
        if (networkResponse.ok) {
            try {
                const cache = await caches.open(CACHE_NAME);
                // Clone the response before putting it in cache
                await cache.put(request, networkResponse.clone());
            } catch (error) {
                console.error('Service Worker: Failed to cache response', error);
            }
        }
        return networkResponse;
    }).catch((error) => {
        console.error('Service Worker: Network request failed', error);
        // Return null on network failure, let the main logic handle fallback
        return null;
    });

    // If we have a cached response, return it immediately
    if (cachedResponse) {
        // Start the network request in the background to update cache
        fetchPromise.catch(() => {}); // Ignore errors for background update
        return cachedResponse;
    }

    // If no cached response, wait for network
    const networkResponse = await fetchPromise;
    if (networkResponse) {
        return networkResponse;
    }

    // If both cache and network failed, return offline response
    return await handleOfflineRequest(request);
}

// Handle offline requests
async function handleOfflineRequest(request) {
    const url = new URL(request.url);

    // For page requests, return offline page
    if (isPageRequest(url)) {
        const offlineResponse = await caches.match(OFFLINE_URL);
        if (offlineResponse) {
            return offlineResponse;
        }

        // Fallback offline page
        return new Response(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Offline</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .offline-icon { font-size: 64px; margin-bottom: 20px; }
                    h1 { color: #333; }
                    p { color: #666; }
                </style>
            </head>
            <body>
                <div class="offline-icon">📡</div>
                <h1>You're Offline</h1>
                <p>Please check your internet connection and try again.</p>
                <button onclick="window.location.reload()">Retry</button>
            </body>
            </html>
        `, {
            headers: { 'Content-Type': 'text/html' }
        });
    }

    // For other requests, return a generic offline response
    return new Response('Offline', { status: 503, statusText: 'Service Unavailable' });
}

// Helper functions to determine resource types
function isStaticResource(url) {
    const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf'];
    return staticExtensions.some(ext => url.pathname.endsWith(ext));
}

function isAPIRequest(url) {
    return url.pathname.includes('/wp-json/') ||
           url.pathname.includes('/wp-admin/admin-ajax.php') ||
           url.pathname.includes('/api/');
}

function isPageRequest(url) {
    return url.pathname.endsWith('/') ||
           url.pathname.endsWith('.html') ||
           url.pathname.endsWith('.php') ||
           (!url.pathname.includes('.') && !url.pathname.includes('/wp-'));
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered', event.tag);

    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    console.log('Service Worker: Performing background sync');

    try {
        // Sync any pending offline actions
        // This could include form submissions, user actions, etc.

        // Example: Sync offline form submissions
        const offlineActions = await getOfflineActions();

        for (const action of offlineActions) {
            try {
                await syncAction(action);
                await removeOfflineAction(action.id);
            } catch (error) {
                console.error('Service Worker: Failed to sync action', error);
            }
        }

        console.log('Service Worker: Background sync completed');
    } catch (error) {
        console.error('Service Worker: Background sync failed', error);
    }
}

async function getOfflineActions() {
    // Get offline actions from IndexedDB or localStorage
    // This is a placeholder - implement based on your needs
    return [];
}

async function syncAction(action) {
    // Sync individual action with server
    // This is a placeholder - implement based on your needs
    console.log('Service Worker: Syncing action', action);
}

async function removeOfflineAction(actionId) {
    // Remove synced action from offline storage
    // This is a placeholder - implement based on your needs
    console.log('Service Worker: Removing offline action', actionId);
}

// Push event handling (enhanced)
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push event received');

    if (!event.data) {
        console.log('Service Worker: Push event has no data');
        return;
    }

    try {
        const data = event.data.json();
        console.log('Service Worker: Push data received', data);

        // Handle different types of push notifications
        if (data.type === 'pwa-update') {
            handlePWAUpdate(data);
        } else {
            // Handle regular push notifications (existing Firebase logic handles this)
        }
    } catch (error) {
        console.error('Service Worker: Error processing push event', error);
    }
});

function handlePWAUpdate(data) {
    // Handle PWA update notifications
    console.log('Service Worker: PWA update available', data);

    // Notify all clients about the update
    self.clients.matchAll().then(clients => {
        clients.forEach(client => {
            client.postMessage({
                type: 'PWA_UPDATE_AVAILABLE',
                data: data
            });
        });
    });
}

// Message handling from main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);

    if (event.data && event.data.type) {
        switch (event.data.type) {
            case 'SKIP_WAITING':
                self.skipWaiting();
                break;
            case 'CACHE_URLS':
                cacheUrls(event.data.urls);
                break;
            case 'CLEAR_CACHE':
                clearCache();
                break;
            case 'GET_CACHED_PAGES':
                getCachedPages(event.source);
                break;
            default:
                console.log('Service Worker: Unknown message type', event.data.type);
        }
    }
});

async function cacheUrls(urls) {
    try {
        const cache = await caches.open(CACHE_NAME);
        await cache.addAll(urls);
        console.log('Service Worker: URLs cached successfully', urls);
    } catch (error) {
        console.error('Service Worker: Failed to cache URLs', error);
    }
}

async function clearCache() {
    try {
        const cacheNames = await caches.keys();
        await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('Service Worker: All caches cleared');
    } catch (error) {
        console.error('Service Worker: Failed to clear cache', error);
    }
}

async function getCachedPages(source) {
    try {
        const cache = await caches.open(CACHE_NAME);
        const requests = await cache.keys();

        const pages = [];

        for (const request of requests) {
            const url = new URL(request.url);

            // Only include page requests (not assets)
            if (isPageRequest(url)) {
                const title = await getPageTitle(request);
                pages.push({
                    url: url.pathname,
                    title: title || url.pathname,
                    icon: getPageIcon(url.pathname)
                });
            }
        }

        // Send the cached pages back to the requesting client
        if (source) {
            source.postMessage({
                type: 'CACHED_PAGES',
                pages: pages
            });
        }

        console.log('Service Worker: Sent cached pages list', pages);
    } catch (error) {
        console.error('Service Worker: Failed to get cached pages', error);
    }
}

async function getPageTitle(request) {
    try {
        const cache = await caches.open(CACHE_NAME);
        const response = await cache.match(request);

        if (response) {
            // Clone the response to avoid consuming the original
            const clonedResponse = response.clone();
            const text = await clonedResponse.text();
            const titleMatch = text.match(/<title[^>]*>([^<]+)<\/title>/i);
            return titleMatch ? titleMatch[1].trim() : null;
        }
    } catch (error) {
        console.error('Service Worker: Failed to get page title', error);
    }
    return null;
}

function getPageIcon(pathname) {
    if (pathname === '/' || pathname === '') return '🏠';
    if (pathname.includes('about')) return 'ℹ️';
    if (pathname.includes('contact')) return '📞';
    if (pathname.includes('blog') || pathname.includes('post')) return '📝';
    if (pathname.includes('shop') || pathname.includes('product')) return '🛒';
    if (pathname.includes('service')) return '⚙️';
    if (pathname.includes('portfolio') || pathname.includes('work')) return '💼';
    return '📄';
}
