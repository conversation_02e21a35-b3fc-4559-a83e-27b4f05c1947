<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\GPBWire;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\InputStream;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A compiled specification for the defaults of a set of features.  These
 * messages are generated from FeatureSet extensions and can be used to seed
 * feature resolution. The resolution with this object becomes a simple search
 * for the closest matching edition, followed by proto merges.
 *
 * Generated from protobuf message <code>google.protobuf.FeatureSetDefaults</code>
 */
class FeatureSetDefaults extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault defaults = 1;</code>
     */
    private $defaults;
    /**
     * The minimum supported edition (inclusive) when this was constructed.
     * Editions before this will not have defaults.
     *
     * Generated from protobuf field <code>optional .google.protobuf.Edition minimum_edition = 4;</code>
     */
    protected $minimum_edition = null;
    /**
     * The maximum known edition (inclusive) when this was constructed. Editions
     * after this will not have reliable defaults.
     *
     * Generated from protobuf field <code>optional .google.protobuf.Edition maximum_edition = 5;</code>
     */
    protected $maximum_edition = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\Google\Protobuf\Internal\FeatureSetDefaults\FeatureSetEditionDefault>|\Google\Protobuf\Internal\RepeatedField $defaults
     *     @type int $minimum_edition
     *           The minimum supported edition (inclusive) when this was constructed.
     *           Editions before this will not have defaults.
     *     @type int $maximum_edition
     *           The maximum known edition (inclusive) when this was constructed. Editions
     *           after this will not have reliable defaults.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Protobuf\Internal\Descriptor::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault defaults = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getDefaults()
    {
        return $this->defaults;
    }

    /**
     * Generated from protobuf field <code>repeated .google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault defaults = 1;</code>
     * @param array<\Google\Protobuf\Internal\FeatureSetDefaults\FeatureSetEditionDefault>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setDefaults($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Protobuf\Internal\FeatureSetDefaults\FeatureSetEditionDefault::class);
        $this->defaults = $arr;

        return $this;
    }

    /**
     * The minimum supported edition (inclusive) when this was constructed.
     * Editions before this will not have defaults.
     *
     * Generated from protobuf field <code>optional .google.protobuf.Edition minimum_edition = 4;</code>
     * @return int
     */
    public function getMinimumEdition()
    {
        return isset($this->minimum_edition) ? $this->minimum_edition : 0;
    }

    public function hasMinimumEdition()
    {
        return isset($this->minimum_edition);
    }

    public function clearMinimumEdition()
    {
        unset($this->minimum_edition);
    }

    /**
     * The minimum supported edition (inclusive) when this was constructed.
     * Editions before this will not have defaults.
     *
     * Generated from protobuf field <code>optional .google.protobuf.Edition minimum_edition = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setMinimumEdition($var)
    {
        GPBUtil::checkEnum($var, \Google\Protobuf\Internal\Edition::class);
        $this->minimum_edition = $var;

        return $this;
    }

    /**
     * The maximum known edition (inclusive) when this was constructed. Editions
     * after this will not have reliable defaults.
     *
     * Generated from protobuf field <code>optional .google.protobuf.Edition maximum_edition = 5;</code>
     * @return int
     */
    public function getMaximumEdition()
    {
        return isset($this->maximum_edition) ? $this->maximum_edition : 0;
    }

    public function hasMaximumEdition()
    {
        return isset($this->maximum_edition);
    }

    public function clearMaximumEdition()
    {
        unset($this->maximum_edition);
    }

    /**
     * The maximum known edition (inclusive) when this was constructed. Editions
     * after this will not have reliable defaults.
     *
     * Generated from protobuf field <code>optional .google.protobuf.Edition maximum_edition = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setMaximumEdition($var)
    {
        GPBUtil::checkEnum($var, \Google\Protobuf\Internal\Edition::class);
        $this->maximum_edition = $var;

        return $this;
    }

}

