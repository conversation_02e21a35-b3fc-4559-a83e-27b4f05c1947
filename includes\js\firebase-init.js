// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.5.0/firebase-app.js";
import { getMessaging } from "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js";

// Validate Firebase configuration
const validateConfig = (config) => {
    if (!config) {
        console.error('Firebase configuration is missing');
        return false;
    }

    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    const missingFields = requiredFields.filter(field => !config[field]);

    if (missingFields.length > 0) {
        console.error('Firebase configuration is missing required fields:', missingFields);
        return false;
    }

    return true;
};

// Initialize Firebase with error handling
let app, messaging;

try {
    if (!validateConfig(window.q_firebase_config)) {
        throw new Error('Invalid Firebase configuration');
    }

    app = initializeApp(window.q_firebase_config);
    messaging = getMessaging(app);
    console.log('Firebase initialized successfully with project:', window.q_firebase_config.projectId);
} catch (error) {
    console.error('Failed to initialize Firebase:', error);
}

// Create a promise to handle Firebase messaging initialization
const messagingPromise = new Promise((resolve, reject) => {
    if (!app || !messaging) {
        reject(new Error('Firebase was not properly initialized'));
        return;
    }

    if (!('serviceWorker' in navigator)) {
        reject(new Error('Service workers are not supported in this browser'));
        return;
    }

    navigator.serviceWorker.register('/firebase-messaging-sw.js', {
        scope: '/',
        type: 'classic'
    })
    .then(registration => {
        console.log('Service worker registration successful:', registration);
        return registration.update();
    })
    .then(() => {
        console.log('Service Worker updated successfully');
        window.qMessaging = messaging;
        window.qMessagingPromise = messagingPromise;
        resolve(messaging);
    })
    .catch(err => {
        console.error('Failed to initialize messaging:', err);
        reject(err);
    });
});

export { messaging, messagingPromise };
