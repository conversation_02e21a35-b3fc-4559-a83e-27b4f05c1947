## Contributing

We are pleased that you are interested in contributing to our work.

### Generated Protocol Buffer Classes

The classes in this repository are generated by the protocol buffer
compiler, as known as protoc. As such, we can not accept contributions
directly to these generated classes. Instead, changes should be
suggested upstream in the [API Common Protos][api-common-protos]
repository.


### Documentation

We want for both protocol buffers and the types that we have provided here
to be understandable to everyone, including to those who may be unfamiliar
with the ecosystem or concepts.

That means we want our documentation to be better, and welcome anyone
willing to help with this. For documentation in the generated classes, please
open a pull request against the [API Common Protos][api-common-protos]
repository.

Any improvements to READMEs or other non-generated documentation or
development scripts in this repository would be greatly appreciated - please
open a pull request.  


## Contributor License Agreement

Before we can accept your pull requests, you will need to sign a Contributor
License Agreement (CLA):

  - **If you are an individual writing original source code** and **you own the
    intellectual property**, then you need to sign an [individual CLA][].
  - **If you work for a company that wants to allow you to contribute your
    work**, then you need to sign a [corporate CLA][].

You can sign these electronically (just scroll to the bottom). After that,
we'll be able to accept your pull requests.

  [individual CLA]: https://developers.google.com/open-source/cla/individual
  [corporate CLA]: https://developers.google.com/open-source/cla/corporate
  [api-common-protos]: https://github.com/googleapis/api-common-protos