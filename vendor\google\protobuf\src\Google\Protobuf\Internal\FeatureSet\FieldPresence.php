<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal\FeatureSet;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.protobuf.FeatureSet.FieldPresence</code>
 */
class FieldPresence
{
    /**
     * Generated from protobuf enum <code>FIELD_PRESENCE_UNKNOWN = 0;</code>
     */
    const FIELD_PRESENCE_UNKNOWN = 0;
    /**
     * Generated from protobuf enum <code>EXPLICIT = 1;</code>
     */
    const EXPLICIT = 1;
    /**
     * Generated from protobuf enum <code>IMPLICIT = 2;</code>
     */
    const IMPLICIT = 2;
    /**
     * Generated from protobuf enum <code>LEGACY_REQUIRED = 3;</code>
     */
    const LEGACY_REQUIRED = 3;

    private static $valueToName = [
        self::FIELD_PRESENCE_UNKNOWN => 'FIELD_PRESENCE_UNKNOWN',
        self::EXPLICIT => 'EXPLICIT',
        self::IMPLICIT => 'IMPLICIT',
        self::LEGACY_REQUIRED => 'LEGACY_REQUIRED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

