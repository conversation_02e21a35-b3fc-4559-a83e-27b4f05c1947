{"packages": [{"name": "beste/clock", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/beste/clock.git", "reference": "7004b55fcd54737b539886244b3a3b2188181974"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beste/clock/zipball/7004b55fcd54737b539886244b3a3b2188181974", "reference": "7004b55fcd54737b539886244b3a3b2188181974", "shasum": ""}, "require": {"php": "^8.0", "psr/clock": "^1.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9.1", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^9.5.26", "psalm/plugin-phpunit": "^0.16.1", "vimeo/psalm": "^4.29"}, "time": "2022-11-26T18:03:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Clock.php"], "psr-4": {"Beste\\Clock\\": "src/Clock"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A collection of Clock implementations", "keywords": ["clock", "clock-interface", "psr-20", "psr20"], "support": {"issues": "https://github.com/beste/clock/issues", "source": "https://github.com/beste/clock/tree/3.0.0"}, "funding": [{"url": "https://github.com/jeromegamez", "type": "github"}], "install-path": "../beste/clock"}, {"name": "beste/in-memory-cache", "version": "1.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/beste/in-memory-cache-php.git", "reference": "f8299adc8abdaf7d309e8b28e53b4307ea49ebc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beste/in-memory-cache-php/zipball/f8299adc8abdaf7d309e8b28e53b4307ea49ebc7", "reference": "f8299adc8abdaf7d309e8b28e53b4307ea49ebc7", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/cache": "^2.0 || ^3.0", "psr/clock": "^1.0"}, "provide": {"psr/cache-implementation": "2.0 || 3.0"}, "require-dev": {"beste/clock": "^3.0", "beste/php-cs-fixer-config": "^3.2.0", "friendsofphp/php-cs-fixer": "^3.62.0", "phpstan/extension-installer": "^1.4.1", "phpstan/phpstan": "^1.11.10", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpunit/phpunit": "^10.5.2 || ^11.3.1", "symfony/var-dumper": "^6.4 || ^7.1.3"}, "suggest": {"psr/clock-implementation": "Allows injecting a Clock, for example a frozen clock for testing"}, "time": "2024-08-26T15:51:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Beste\\Cache\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PSR-6 In-Memory cache that can be used as a fallback implementation and/or in tests.", "keywords": ["beste", "cache", "psr-6"], "support": {"issues": "https://github.com/beste/in-memory-cache-php/issues", "source": "https://github.com/beste/in-memory-cache-php/tree/1.3.1"}, "funding": [{"url": "https://github.com/jeromegamez", "type": "github"}], "install-path": "../beste/in-memory-cache"}, {"name": "beste/json", "version": "1.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/beste/json.git", "reference": "0e9a0dc74fa6d1bb4f9883ef64fa9f36b7b6b934"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beste/json/zipball/0e9a0dc74fa6d1bb4f9883ef64fa9f36b7b6b934", "reference": "0e9a0dc74fa6d1bb4f9883ef64fa9f36b7b6b934", "shasum": ""}, "require": {"ext-json": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpstan/phpstan": "^2.0.4", "phpstan/phpstan-phpunit": "^2.0.2", "phpstan/phpstan-strict-rules": "^2.0.1", "phpunit/phpunit": "^10.4.2", "rector/rector": "^2.0.3"}, "time": "2024-12-19T09:35:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Json.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A simple JSON helper to decode and encode JSON", "keywords": ["helper", "json"], "support": {"issues": "https://github.com/beste/json/issues", "source": "https://github.com/beste/json/tree/1.5.1"}, "funding": [{"url": "https://github.com/jeromegamez", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/beste/json", "type": "tidelift"}], "install-path": "../beste/json"}, {"name": "brick/math", "version": "0.12.3", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/866551da34e9a618e64a819ee1e01c20d8a588ba", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "time": "2025-02-28T13:11:00+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "install-path": "../brick/math"}, {"name": "fig/http-message-util", "version": "1.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message-util.git", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message-util/zipball/9d94dc0154230ac39e5bf89398b324a86f63f765", "reference": "9d94dc0154230ac39e5bf89398b324a86f63f765", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "suggest": {"psr/http-message": "The package containing the PSR-7 interfaces"}, "time": "2020-11-24T22:02:12+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Fig\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Utility classes and constants for use with PSR-7 (psr/http-message)", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-message-util/issues", "source": "https://github.com/php-fig/http-message-util/tree/1.1.5"}, "install-path": "../fig/http-message-util"}, {"name": "firebase/php-jwt", "version": "v6.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "8f718f4dfc9c5d5f0c994cdfd103921b43592712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/8f718f4dfc9c5d5f0c994cdfd103921b43592712", "reference": "8f718f4dfc9c5d5f0c994cdfd103921b43592712", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2025-01-23T05:11:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.0"}, "install-path": "../firebase/php-jwt"}, {"name": "google/auth", "version": "v1.46.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "7fafae99a41984cbfb92508174263cf7bf3049b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/7fafae99a41984cbfb92508174263cf7bf3049b9", "reference": "7fafae99a41984cbfb92508174263cf7bf3049b9", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.4.5", "php": "^8.0", "psr/cache": "^2.0||^3.0", "psr/http-message": "^1.1||^2.0", "psr/log": "^3.0"}, "require-dev": {"guzzlehttp/promises": "^2.0", "kelvinmo/simplejwt": "0.7.1", "phpseclib/phpseclib": "^3.0.35", "phpspec/prophecy-phpunit": "^2.1", "phpunit/phpunit": "^9.6", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^6.0||^7.0", "webmozart/assert": "^1.11"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "time": "2025-02-12T22:21:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "https://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://cloud.google.com/php/docs/reference/auth/latest", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.46.0"}, "install-path": "../google/auth"}, {"name": "google/cloud-core", "version": "v1.62.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-core.git", "reference": "824a617d5c2b1b571673d1111856f5c2f064a0fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-core/zipball/824a617d5c2b1b571673d1111856f5c2f064a0fd", "reference": "824a617d5c2b1b571673d1111856f5c2f064a0fd", "shasum": ""}, "require": {"google/auth": "^1.34", "google/gax": "^1.36.0", "guzzlehttp/guzzle": "^6.5.8|^7.4.4", "guzzlehttp/promises": "^1.4||^2.0", "guzzlehttp/psr7": "^2.6", "monolog/monolog": "^2.9|^3.0", "php": "^8.0", "psr/http-message": "^1.0|^2.0", "rize/uri-template": "~0.3"}, "require-dev": {"erusev/parsedown": "^1.6", "google/cloud-common-protos": "~0.5", "opis/closure": "^3", "phpdocumentor/reflection": "^5.3.3||^6.0", "phpdocumentor/reflection-docblock": "^5.3", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"opis/closure": "May be used to serialize closures to process jobs in the batch daemon. Please require version ^3.", "symfony/lock": "Required for the Spanner cached based session pool. Please require the following commit: 3.3.x-dev#1ba6ac9"}, "time": "2025-02-22T00:57:16+00:00", "bin": ["bin/google-cloud-batch"], "type": "library", "extra": {"component": {"id": "cloud-core", "path": "Core", "entry": "src/ServiceBuilder.php", "target": "googleapis/google-cloud-php-core.git"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\Cloud\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Cloud PHP shared dependency, providing functionality useful to all components.", "support": {"source": "https://github.com/googleapis/google-cloud-php-core/tree/v1.62.1"}, "install-path": "../google/cloud-core"}, {"name": "google/cloud-storage", "version": "v1.47.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-storage.git", "reference": "c8f130fab6a48dad9c486e93a07cd26123d006bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-storage/zipball/c8f130fab6a48dad9c486e93a07cd26123d006bb", "reference": "c8f130fab6a48dad9c486e93a07cd26123d006bb", "shasum": ""}, "require": {"google/cloud-core": "^1.57", "php": "^8.0", "ramsey/uuid": "^4.2.3"}, "require-dev": {"erusev/parsedown": "^1.6", "google/cloud-pubsub": "^2.0", "phpdocumentor/reflection": "^5.3.3||^6.0", "phpdocumentor/reflection-docblock": "^5.3", "phpseclib/phpseclib": "^2.0||^3.0", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"google/cloud-pubsub": "May be used to register a topic to receive bucket notifications.", "phpseclib/phpseclib": "May be used in place of OpenSSL for creating signed Cloud Storage URLs. Please require version ^2."}, "time": "2025-03-21T22:19:41+00:00", "type": "library", "extra": {"component": {"id": "cloud-storage", "path": "Storage", "entry": "src/StorageClient.php", "target": "googleapis/google-cloud-php-storage.git"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\Cloud\\Storage\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Cloud Storage Client for PHP", "support": {"source": "https://github.com/googleapis/google-cloud-php-storage/tree/v1.47.0"}, "install-path": "../google/cloud-storage"}, {"name": "google/common-protos", "version": "4.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/googleapis/common-protos-php.git", "reference": "2554ed1f09aa20faae7b71b590e7063df97ff670"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/common-protos-php/zipball/2554ed1f09aa20faae7b71b590e7063df97ff670", "reference": "2554ed1f09aa20faae7b71b590e7063df97ff670", "shasum": ""}, "require": {"google/protobuf": "^v3.25.3||^4.26.1", "php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "time": "2025-02-18T19:46:55+00:00", "type": "library", "extra": {"component": {"id": "common-protos", "path": "CommonProtos", "entry": "README.md", "target": "googleapis/common-protos-php.git"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\Api\\": "src/Api", "Google\\Iam\\": "src/Iam", "Google\\Rpc\\": "src/Rpc", "Google\\Type\\": "src/Type", "Google\\Cloud\\": "src/Cloud", "GPBMetadata\\Google\\Api\\": "metadata/Api", "GPBMetadata\\Google\\Iam\\": "metadata/Iam", "GPBMetadata\\Google\\Rpc\\": "metadata/Rpc", "GPBMetadata\\Google\\Type\\": "metadata/Type", "GPBMetadata\\Google\\Cloud\\": "metadata/Cloud", "GPBMetadata\\Google\\Logging\\": "metadata/Logging"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google API Common Protos for PHP", "homepage": "https://github.com/googleapis/common-protos-php", "keywords": ["google"], "support": {"source": "https://github.com/googleapis/common-protos-php/tree/v4.11.0"}, "install-path": "../google/common-protos"}, {"name": "google/gax", "version": "v1.36.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/googleapis/gax-php.git", "reference": "140599cf5eae2432363ce6198e9fdff851625a7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/gax-php/zipball/140599cf5eae2432363ce6198e9fdff851625a7a", "reference": "140599cf5eae2432363ce6198e9fdff851625a7a", "shasum": ""}, "require": {"google/auth": "^1.45", "google/common-protos": "^4.4", "google/grpc-gcp": "^0.4", "google/longrunning": "~0.4", "google/protobuf": "^v3.25.3||^4.26.1", "grpc/grpc": "^1.13", "guzzlehttp/promises": "^2.0", "guzzlehttp/psr7": "^2.0", "php": "^8.0", "ramsey/uuid": "^4.0"}, "conflict": {"ext-protobuf": "<3.7.0"}, "require-dev": {"phpspec/prophecy-phpunit": "^2.1", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "3.*"}, "time": "2024-12-11T02:47:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\ApiCore\\": "src", "GPBMetadata\\ApiCore\\": "metadata/ApiCore"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Google API Core for PHP", "homepage": "https://github.com/googleapis/gax-php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/gax-php/issues", "source": "https://github.com/googleapis/gax-php/tree/v1.36.0"}, "install-path": "../google/gax"}, {"name": "google/grpc-gcp", "version": "v0.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/grpc-gcp-php.git", "reference": "e585b7721bbe806ef45b5c52ae43dfc2bff89968"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GoogleCloudPlatform/grpc-gcp-php/zipball/e585b7721bbe806ef45b5c52ae43dfc2bff89968", "reference": "e585b7721bbe806ef45b5c52ae43dfc2bff89968", "shasum": ""}, "require": {"google/auth": "^1.3", "google/protobuf": "^v3.25.3||^4.26.1", "grpc/grpc": "^v1.13.0", "php": "^8.0", "psr/cache": "^1.0.1||^2.0.0||^3.0.0"}, "require-dev": {"google/cloud-spanner": "^1.7", "phpunit/phpunit": "^9.0"}, "time": "2025-02-19T21:53:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Grpc\\Gcp\\": "src/"}, "classmap": ["src/generated/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC GCP library for channel management", "support": {"issues": "https://github.com/GoogleCloudPlatform/grpc-gcp-php/issues", "source": "https://github.com/GoogleCloudPlatform/grpc-gcp-php/tree/v0.4.1"}, "install-path": "../google/grpc-gcp"}, {"name": "google/longrunning", "version": "0.4.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/googleapis/php-longrunning.git", "reference": "624cabb874c10e5ddc9034c999f724894b70a3d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/php-longrunning/zipball/624cabb874c10e5ddc9034c999f724894b70a3d3", "reference": "624cabb874c10e5ddc9034c999f724894b70a3d3", "shasum": ""}, "require-dev": {"google/gax": "^1.36.0", "phpunit/phpunit": "^9.0"}, "time": "2025-01-24T21:24:06+00:00", "type": "library", "extra": {"component": {"id": "longrunning", "path": "<PERSON><PERSON><PERSON>ning", "entry": null, "target": "googleapis/php-longrunning"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\LongRunning\\": "src/LongRunning", "Google\\ApiCore\\LongRunning\\": "src/ApiCore/LongRunning", "GPBMetadata\\Google\\Longrunning\\": "metadata/Longrunning"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google LongRunning Client for PHP", "support": {"source": "https://github.com/googleapis/php-longrunning/tree/v0.4.7"}, "install-path": "../google/longrunning"}, {"name": "google/protobuf", "version": "v4.30.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "f29ba8a30dfd940efb3a8a75dc44446539101f24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/f29ba8a30dfd940efb3a8a75dc44446539101f24", "reference": "f29ba8a30dfd940efb3a8a75dc44446539101f24", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": ">=5.0.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "time": "2025-03-13T21:08:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"source": "https://github.com/protocolbuffers/protobuf-php/tree/v4.30.1"}, "install-path": "../google/protobuf"}, {"name": "grpc/grpc", "version": "1.57.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "b610c42022ed3a22f831439cb93802f2a4502fdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/b610c42022ed3a22f831439cb93802f2a4502fdf", "reference": "b610c42022ed3a22f831439cb93802f2a4502fdf", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "time": "2023-08-14T23:57:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "support": {"source": "https://github.com/grpc/grpc-php/tree/v1.57.0"}, "install-path": "../grpc/grpc"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2024-07-24T11:22:20+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "time": "2024-10-17T10:06:22+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2024-07-18T11:15:46+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "kreait/firebase-php", "version": "7.18.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/kreait/firebase-php.git", "reference": "f43bc2509721ae2bff77f5232e5f56225440393b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kreait/firebase-php/zipball/f43bc2509721ae2bff77f5232e5f56225440393b", "reference": "f43bc2509721ae2bff77f5232e5f56225440393b", "shasum": ""}, "require": {"beste/clock": "^3.0", "beste/in-memory-cache": "^1.3.1", "beste/json": "^1.5.1", "ext-ctype": "*", "ext-filter": "*", "ext-json": "*", "ext-mbstring": "*", "fig/http-message-util": "^1.1.5", "firebase/php-jwt": "^6.10.2", "google/auth": "^v1.45", "google/cloud-storage": "^1.45", "guzzlehttp/guzzle": "^7.9.2", "guzzlehttp/promises": "^2.0.4", "guzzlehttp/psr7": "^2.7", "kreait/firebase-tokens": "^5.2", "lcobucci/jwt": "^4.3|^5.3", "mtdowling/jmespath.php": "^2.8.0", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/cache": "^1.0.1|^2.0|^3.0", "psr/clock": "^1.0", "psr/http-client": "^1.0.3", "psr/http-factory": "^1.1", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^1.1|^2.0|^3.0.2"}, "require-dev": {"google/cloud-firestore": "^1.47.2", "php-cs-fixer/shim": "^3.68.5", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.0.4", "phpstan/phpstan-deprecation-rules": "^2.0.1", "phpstan/phpstan-phpunit": "^2.0.3", "phpunit/phpunit": "^10.5.39", "rector/rector": "^2.0.3", "shipmonk/composer-dependency-analyser": "^1.8.1", "symfony/var-dumper": "^6.4.15 || ^7.2.0", "vlucas/phpdotenv": "^5.6.1"}, "suggest": {"google/cloud-firestore": "^1.0 to use the Firestore component"}, "time": "2025-03-08T22:54:18+00:00", "type": "library", "extra": {"branch-alias": {"dev-7.x": "7.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Kreait\\Firebase\\": "src/Firebase"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://github.com/jeromegamez"}], "description": "Firebase Admin SDK", "homepage": "https://github.com/kreait/firebase-php", "keywords": ["api", "database", "firebase", "google", "sdk"], "support": {"docs": "https://firebase-php.readthedocs.io", "issues": "https://github.com/kreait/firebase-php/issues", "source": "https://github.com/kreait/firebase-php"}, "funding": [{"url": "https://github.com/sponsors/jeromegamez", "type": "github"}], "install-path": "../kreait/firebase-php"}, {"name": "kreait/firebase-tokens", "version": "5.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/kreait/firebase-tokens-php.git", "reference": "df6f9d153f3bbe671c3247576d2a45cbd0a79620"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kreait/firebase-tokens-php/zipball/df6f9d153f3bbe671c3247576d2a45cbd0a79620", "reference": "df6f9d153f3bbe671c3247576d2a45cbd0a79620", "shasum": ""}, "require": {"beste/clock": "^3.0", "ext-json": "*", "ext-openssl": "*", "fig/http-message-util": "^1.1.5", "guzzlehttp/guzzle": "^7.8", "lcobucci/jwt": "^5.2", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/cache": "^1.0|^2.0|^3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.62.0", "phpstan/extension-installer": "^1.4.1", "phpstan/phpstan": "^1.11.10", "phpstan/phpstan-phpunit": "^1.4.0", "phpunit/phpunit": "^10.5.30", "rector/rector": "^1.2.3", "symfony/cache": "^6.4.3 || ^7.1.3", "symfony/var-dumper": "^6.4.3 || ^7.1.3"}, "suggest": {"psr/cache-implementation": "to cache fetched remote public keys"}, "time": "2024-12-20T11:29:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Kreait\\Firebase\\JWT\\": "src/JWT"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://github.com/jeromegamez"}], "description": "A library to work with Firebase tokens", "homepage": "https://github.com/kreait/firebase-token-php", "keywords": ["Authentication", "auth", "firebase", "google", "token"], "support": {"issues": "https://github.com/kreait/firebase-tokens-php/issues", "source": "https://github.com/kreait/firebase-tokens-php/tree/5.2.1"}, "funding": [{"url": "https://github.com/sponsors/jeromegamez", "type": "github"}], "install-path": "../kreait/firebase-tokens"}, {"name": "lcobucci/jwt", "version": "5.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "a835af59b030d3f2967725697cf88300f579088e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/a835af59b030d3f2967725697cf88300f579088e", "reference": "a835af59b030d3f2967725697cf88300f579088e", "shasum": ""}, "require": {"ext-openssl": "*", "ext-sodium": "*", "php": "~8.2.0 || ~8.3.0 || ~8.4.0", "psr/clock": "^1.0"}, "require-dev": {"infection/infection": "^0.29", "lcobucci/clock": "^3.2", "lcobucci/coding-standard": "^11.0", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.10.7", "phpstan/phpstan-deprecation-rules": "^1.1.3", "phpstan/phpstan-phpunit": "^1.3.10", "phpstan/phpstan-strict-rules": "^1.5.0", "phpunit/phpunit": "^11.1"}, "suggest": {"lcobucci/clock": ">= 3.2"}, "time": "2025-01-26T21:29:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/5.5.0"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "install-path": "../lcobucci/jwt"}, {"name": "monolog/monolog", "version": "3.9.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2025-03-24T10:02:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "mtdowling/jmespath.php", "version": "2.8.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "time": "2024-09-04T18:46:31+00:00", "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.8.0"}, "install-path": "../mtdowling/jmespath.php"}, {"name": "psr/cache", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-02-03T23:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "install-path": "../psr/cache"}, {"name": "psr/clock", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "time": "2022-11-25T14:36:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "install-path": "../psr/clock"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "time": "2024-04-15T12:06:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2024-09-11T13:17:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "install-path": "../psr/log"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "ramsey/collection", "version": "2.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "time": "2025-03-22T05:38:12+00:00", "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "installation-source": "dist", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "install-path": "../ramsey/collection"}, {"name": "ramsey/uuid", "version": "4.7.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "91039bc1faa45ba123c4328958e620d382ec7088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/91039bc1faa45ba123c4328958e620d382ec7088", "reference": "91039bc1faa45ba123c4328958e620d382ec7088", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "time": "2024-04-27T21:32:50+00:00", "type": "library", "extra": {"captainhook": {"force-install": true}}, "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.6"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "install-path": "../ramsey/uuid"}, {"name": "rize/uri-template", "version": "0.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/rize/UriTemplate.git", "reference": "56f374a9a42c7c3998f8b55b6b21b224de90c58b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rize/UriTemplate/zipball/56f374a9a42c7c3998f8b55b6b21b224de90c58b", "reference": "56f374a9a42c7c3998f8b55b6b21b224de90c58b", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.63", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "~10.0"}, "time": "2024-11-27T12:13:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Rize\\": "src/Rize"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>ut <PERSON>", "homepage": "http://twitter.com/rezigned"}], "description": "PHP URI Template (RFC 6570) supports both expansion & extraction", "keywords": ["RFC 6570", "template", "uri"], "support": {"issues": "https://github.com/rize/UriTemplate/issues", "source": "https://github.com/rize/UriTemplate/tree/0.4.0"}, "funding": [{"url": "https://www.paypal.me/rezigned", "type": "custom"}, {"url": "https://github.com/rezigned", "type": "github"}, {"url": "https://opencollective.com/rize-uri-template", "type": "open_collective"}], "install-path": "../rize/uri-template"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}], "dev": true, "dev-package-names": []}