<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_Settings {
    public static function init() {
        add_action('admin_menu', [self::class, 'add_settings_page']);
        add_action('admin_init', [self::class, 'register_settings']);
    }

    public static function add_settings_page() {
        add_submenu_page(
            'options-general.php',
            'Q Pusher Settings',
            'Q Pusher',
            'manage_options',
            'q-pusher-settings',
            [self::class, 'render_settings_page']
        );
    }

    public static function register_settings() {
        // Client-side Firebase config
        register_setting(
            'q_pusher_settings',
            'q_firebase_config',
            [
                'sanitize_callback' => [self::class, 'validate_firebase_config']
            ]
        );
        
        // Server-side service account
        register_setting(
            'q_pusher_settings',
            'q_firebase_service_account',
            [
                'sanitize_callback' => [self::class, 'validate_service_account']
            ]
        );
        
        add_settings_section(
            'q_firebase_section',
            'Firebase Configuration',
            null,
            'q_pusher_settings'
        );

        add_settings_field(
            'q_firebase_config',
            'Firebase Config JSON (Client-side)',
            [self::class, 'render_firebase_config_field'],
            'q_pusher_settings',
            'q_firebase_section'
        );

        add_settings_field(
            'q_firebase_service_account',
            'Service Account JSON (Server-side)',
            [self::class, 'render_service_account_field'],
            'q_pusher_settings',
            'q_firebase_section'
        );
    }

    public static function render_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form action="options.php" method="post">
                <?php
                settings_fields('q_pusher_settings');
                do_settings_sections('q_pusher_settings');
                submit_button('Save Settings');
                ?>
            </form>
        </div>
        <?php
    }

    public static function render_firebase_config_field() {
        $value = get_option('q_firebase_config');
        ?>
        <textarea name="q_firebase_config" rows="10" cols="50" class="large-text code"><?php echo esc_textarea($value); ?></textarea>
        <p class="description">Paste your Firebase configuration as JSON. Convert your Firebase config to JSON format like this:</p>
        <pre style="background: #f0f0f0; padding: 10px;">
{
    "apiKey": "your-api-key",
    "authDomain": "your-project.firebaseapp.com",
    "projectId": "your-project",
    "storageBucket": "your-project.appspot.com",
    "messagingSenderId": "123456789",
    "appId": "1:123456789:web:abcdef",
    "measurementId": "G-ABCDEF123"
}</pre>
        <p class="description">Note: Remove any JavaScript code and ensure it's valid JSON (use double quotes, no comments, no trailing commas).</p>
        <?php
    }

    public static function validate_firebase_config($value) {
        // Remove any JavaScript-specific parts
        $value = preg_replace('/const\s+firebaseConfig\s+=\s+/i', '', $value);
        $value = preg_replace('/;$/', '', $value);
        
        // Try to decode to validate JSON
        $decoded = json_decode($value, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            add_settings_error(
                'q_firebase_config',
                'invalid_json',
                'Firebase configuration must be valid JSON. Please remove any JavaScript code and ensure proper JSON formatting.'
            );
            return get_option('q_firebase_config'); // Return old value
        }
        
        // Validate required fields
        $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
        foreach ($required_fields as $field) {
            if (empty($decoded[$field])) {
                add_settings_error(
                    'q_firebase_config',
                    'missing_field',
                    "Firebase configuration is missing required field: {$field}"
                );
                return get_option('q_firebase_config'); // Return old value
            }
        }
        
        return json_encode($decoded, JSON_PRETTY_PRINT);
    }

    public static function render_service_account_field() {
        $value = get_option('q_firebase_service_account');
        ?>
        <textarea name="q_firebase_service_account" rows="10" cols="50" class="large-text code"><?php echo esc_textarea($value); ?></textarea>
        <p class="description">Paste your Firebase Service Account JSON here. To get this:</p>
        <ol>
            <li>Go to Firebase Console → Project Settings → Service Accounts</li>
            <li>Click "Generate New Private Key"</li>
            <li>Copy the contents of the downloaded JSON file here</li>
        </ol>
        <p class="description">The service account JSON should include fields like: type, project_id, private_key_id, private_key, client_email, etc.</p>
        <?php
    }

    public static function validate_service_account($value) {
        if (empty($value)) {
            add_settings_error(
                'q_firebase_service_account',
                'empty_service_account',
                'Service Account JSON is required'
            );
            return get_option('q_firebase_service_account');
        }

        $decoded = json_decode($value, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            add_settings_error(
                'q_firebase_service_account',
                'invalid_json',
                'Service Account must be valid JSON'
            );
            return get_option('q_firebase_service_account');
        }

        // Validate required fields for service account
        $required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email', 'client_id'];
        foreach ($required_fields as $field) {
            if (empty($decoded[$field])) {
                add_settings_error(
                    'q_firebase_service_account',
                    'missing_field',
                    "Service Account JSON is missing required field: {$field}"
                );
                return get_option('q_firebase_service_account');
            }
        }

        return json_encode($decoded, JSON_PRETTY_PRINT);
    }
}
