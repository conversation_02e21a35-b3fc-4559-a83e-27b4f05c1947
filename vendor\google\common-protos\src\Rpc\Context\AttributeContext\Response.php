<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/context/attribute_context.proto

namespace Google\Rpc\Context\AttributeContext;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * This message defines attributes for a typical network response. It
 * generally models semantics of an HTTP response.
 *
 * Generated from protobuf message <code>google.rpc.context.AttributeContext.Response</code>
 */
class Response extends \Google\Protobuf\Internal\Message
{
    /**
     * The HTTP response status code, such as `200` and `404`.
     *
     * Generated from protobuf field <code>int64 code = 1;</code>
     */
    protected $code = 0;
    /**
     * The HTTP response size in bytes. If unknown, it must be -1.
     *
     * Generated from protobuf field <code>int64 size = 2;</code>
     */
    protected $size = 0;
    /**
     * The HTTP response headers. If multiple headers share the same key, they
     * must be merged according to HTTP spec. All header keys must be
     * lowercased, because HTTP header keys are case-insensitive.
     *
     * Generated from protobuf field <code>map<string, string> headers = 3;</code>
     */
    private $headers;
    /**
     * The timestamp when the `destination` service sends the last byte of
     * the response.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp time = 4;</code>
     */
    protected $time = null;
    /**
     * The amount of time it takes the backend service to fully respond to a
     * request. Measured from when the destination service starts to send the
     * request to the backend until when the destination service receives the
     * complete response from the backend.
     *
     * Generated from protobuf field <code>.google.protobuf.Duration backend_latency = 5;</code>
     */
    protected $backend_latency = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $code
     *           The HTTP response status code, such as `200` and `404`.
     *     @type int|string $size
     *           The HTTP response size in bytes. If unknown, it must be -1.
     *     @type array|\Google\Protobuf\Internal\MapField $headers
     *           The HTTP response headers. If multiple headers share the same key, they
     *           must be merged according to HTTP spec. All header keys must be
     *           lowercased, because HTTP header keys are case-insensitive.
     *     @type \Google\Protobuf\Timestamp $time
     *           The timestamp when the `destination` service sends the last byte of
     *           the response.
     *     @type \Google\Protobuf\Duration $backend_latency
     *           The amount of time it takes the backend service to fully respond to a
     *           request. Measured from when the destination service starts to send the
     *           request to the backend until when the destination service receives the
     *           complete response from the backend.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Rpc\Context\AttributeContext::initOnce();
        parent::__construct($data);
    }

    /**
     * The HTTP response status code, such as `200` and `404`.
     *
     * Generated from protobuf field <code>int64 code = 1;</code>
     * @return int|string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * The HTTP response status code, such as `200` and `404`.
     *
     * Generated from protobuf field <code>int64 code = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCode($var)
    {
        GPBUtil::checkInt64($var);
        $this->code = $var;

        return $this;
    }

    /**
     * The HTTP response size in bytes. If unknown, it must be -1.
     *
     * Generated from protobuf field <code>int64 size = 2;</code>
     * @return int|string
     */
    public function getSize()
    {
        return $this->size;
    }

    /**
     * The HTTP response size in bytes. If unknown, it must be -1.
     *
     * Generated from protobuf field <code>int64 size = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setSize($var)
    {
        GPBUtil::checkInt64($var);
        $this->size = $var;

        return $this;
    }

    /**
     * The HTTP response headers. If multiple headers share the same key, they
     * must be merged according to HTTP spec. All header keys must be
     * lowercased, because HTTP header keys are case-insensitive.
     *
     * Generated from protobuf field <code>map<string, string> headers = 3;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getHeaders()
    {
        return $this->headers;
    }

    /**
     * The HTTP response headers. If multiple headers share the same key, they
     * must be merged according to HTTP spec. All header keys must be
     * lowercased, because HTTP header keys are case-insensitive.
     *
     * Generated from protobuf field <code>map<string, string> headers = 3;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setHeaders($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->headers = $arr;

        return $this;
    }

    /**
     * The timestamp when the `destination` service sends the last byte of
     * the response.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp time = 4;</code>
     * @return \Google\Protobuf\Timestamp|null
     */
    public function getTime()
    {
        return $this->time;
    }

    public function hasTime()
    {
        return isset($this->time);
    }

    public function clearTime()
    {
        unset($this->time);
    }

    /**
     * The timestamp when the `destination` service sends the last byte of
     * the response.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp time = 4;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->time = $var;

        return $this;
    }

    /**
     * The amount of time it takes the backend service to fully respond to a
     * request. Measured from when the destination service starts to send the
     * request to the backend until when the destination service receives the
     * complete response from the backend.
     *
     * Generated from protobuf field <code>.google.protobuf.Duration backend_latency = 5;</code>
     * @return \Google\Protobuf\Duration|null
     */
    public function getBackendLatency()
    {
        return $this->backend_latency;
    }

    public function hasBackendLatency()
    {
        return isset($this->backend_latency);
    }

    public function clearBackendLatency()
    {
        unset($this->backend_latency);
    }

    /**
     * The amount of time it takes the backend service to fully respond to a
     * request. Measured from when the destination service starts to send the
     * request to the backend until when the destination service receives the
     * complete response from the backend.
     *
     * Generated from protobuf field <code>.google.protobuf.Duration backend_latency = 5;</code>
     * @param \Google\Protobuf\Duration $var
     * @return $this
     */
    public function setBackendLatency($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Duration::class);
        $this->backend_latency = $var;

        return $this;
    }

}


