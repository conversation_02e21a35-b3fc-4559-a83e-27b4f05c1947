<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/resource.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A simple descriptor of a resource type.
 * ResourceDescriptor annotates a resource message (either by means of a
 * protobuf annotation or use in the service config), and associates the
 * resource's schema, the resource type, and the pattern of the resource name.
 * Example:
 *     message Topic {
 *       // Indicates this message defines a resource schema.
 *       // Declares the resource type in the format of {service}/{kind}.
 *       // For Kubernetes resources, the format is {api group}/{kind}.
 *       option (google.api.resource) = {
 *         type: "pubsub.googleapis.com/Topic"
 *         pattern: "projects/{project}/topics/{topic}"
 *       };
 *     }
 * The ResourceDescriptor Yaml config will look like:
 *     resources:
 *     - type: "pubsub.googleapis.com/Topic"
 *       pattern: "projects/{project}/topics/{topic}"
 * Sometimes, resources have multiple patterns, typically because they can
 * live under multiple parents.
 * Example:
 *     message LogEntry {
 *       option (google.api.resource) = {
 *         type: "logging.googleapis.com/LogEntry"
 *         pattern: "projects/{project}/logs/{log}"
 *         pattern: "folders/{folder}/logs/{log}"
 *         pattern: "organizations/{organization}/logs/{log}"
 *         pattern: "billingAccounts/{billing_account}/logs/{log}"
 *       };
 *     }
 * The ResourceDescriptor Yaml config will look like:
 *     resources:
 *     - type: 'logging.googleapis.com/LogEntry'
 *       pattern: "projects/{project}/logs/{log}"
 *       pattern: "folders/{folder}/logs/{log}"
 *       pattern: "organizations/{organization}/logs/{log}"
 *       pattern: "billingAccounts/{billing_account}/logs/{log}"
 *
 * Generated from protobuf message <code>google.api.ResourceDescriptor</code>
 */
class ResourceDescriptor extends \Google\Protobuf\Internal\Message
{
    /**
     * The resource type. It must be in the format of
     * {service_name}/{resource_type_kind}. The `resource_type_kind` must be
     * singular and must not include version numbers.
     * Example: `storage.googleapis.com/Bucket`
     * The value of the resource_type_kind must follow the regular expression
     * /[A-Za-z][a-zA-Z0-9]+/. It should start with an upper case character and
     * should use PascalCase (UpperCamelCase). The maximum number of
     * characters allowed for the `resource_type_kind` is 100.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     */
    protected $type = '';
    /**
     * Optional. The relative resource name pattern associated with this resource
     * type. The DNS prefix of the full resource name shouldn't be specified here.
     * The path pattern must follow the syntax, which aligns with HTTP binding
     * syntax:
     *     Template = Segment { "/" Segment } ;
     *     Segment = LITERAL | Variable ;
     *     Variable = "{" LITERAL "}" ;
     * Examples:
     *     - "projects/{project}/topics/{topic}"
     *     - "projects/{project}/knowledgeBases/{knowledge_base}"
     * The components in braces correspond to the IDs for each resource in the
     * hierarchy. It is expected that, if multiple patterns are provided,
     * the same component name (e.g. "project") refers to IDs of the same
     * type of resource.
     *
     * Generated from protobuf field <code>repeated string pattern = 2;</code>
     */
    private $pattern;
    /**
     * Optional. The field on the resource that designates the resource name
     * field. If omitted, this is assumed to be "name".
     *
     * Generated from protobuf field <code>string name_field = 3;</code>
     */
    protected $name_field = '';
    /**
     * Optional. The historical or future-looking state of the resource pattern.
     * Example:
     *     // The InspectTemplate message originally only supported resource
     *     // names with organization, and project was added later.
     *     message InspectTemplate {
     *       option (google.api.resource) = {
     *         type: "dlp.googleapis.com/InspectTemplate"
     *         pattern:
     *         "organizations/{organization}/inspectTemplates/{inspect_template}"
     *         pattern: "projects/{project}/inspectTemplates/{inspect_template}"
     *         history: ORIGINALLY_SINGLE_PATTERN
     *       };
     *     }
     *
     * Generated from protobuf field <code>.google.api.ResourceDescriptor.History history = 4;</code>
     */
    protected $history = 0;
    /**
     * The plural name used in the resource name and permission names, such as
     * 'projects' for the resource name of 'projects/{project}' and the permission
     * name of 'cloudresourcemanager.googleapis.com/projects.get'. One exception
     * to this is for Nested Collections that have stuttering names, as defined
     * in [AIP-122](https://google.aip.dev/122#nested-collections), where the
     * collection ID in the resource name pattern does not necessarily directly
     * match the `plural` value.
     * It is the same concept of the `plural` field in k8s CRD spec
     * https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     * Note: The plural form is required even for singleton resources. See
     * https://aip.dev/156
     *
     * Generated from protobuf field <code>string plural = 5;</code>
     */
    protected $plural = '';
    /**
     * The same concept of the `singular` field in k8s CRD spec
     * https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     * Such as "project" for the `resourcemanager.googleapis.com/Project` type.
     *
     * Generated from protobuf field <code>string singular = 6;</code>
     */
    protected $singular = '';
    /**
     * Style flag(s) for this resource.
     * These indicate that a resource is expected to conform to a given
     * style. See the specific style flags for additional information.
     *
     * Generated from protobuf field <code>repeated .google.api.ResourceDescriptor.Style style = 10;</code>
     */
    private $style;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $type
     *           The resource type. It must be in the format of
     *           {service_name}/{resource_type_kind}. The `resource_type_kind` must be
     *           singular and must not include version numbers.
     *           Example: `storage.googleapis.com/Bucket`
     *           The value of the resource_type_kind must follow the regular expression
     *           /[A-Za-z][a-zA-Z0-9]+/. It should start with an upper case character and
     *           should use PascalCase (UpperCamelCase). The maximum number of
     *           characters allowed for the `resource_type_kind` is 100.
     *     @type array<string>|\Google\Protobuf\Internal\RepeatedField $pattern
     *           Optional. The relative resource name pattern associated with this resource
     *           type. The DNS prefix of the full resource name shouldn't be specified here.
     *           The path pattern must follow the syntax, which aligns with HTTP binding
     *           syntax:
     *               Template = Segment { "/" Segment } ;
     *               Segment = LITERAL | Variable ;
     *               Variable = "{" LITERAL "}" ;
     *           Examples:
     *               - "projects/{project}/topics/{topic}"
     *               - "projects/{project}/knowledgeBases/{knowledge_base}"
     *           The components in braces correspond to the IDs for each resource in the
     *           hierarchy. It is expected that, if multiple patterns are provided,
     *           the same component name (e.g. "project") refers to IDs of the same
     *           type of resource.
     *     @type string $name_field
     *           Optional. The field on the resource that designates the resource name
     *           field. If omitted, this is assumed to be "name".
     *     @type int $history
     *           Optional. The historical or future-looking state of the resource pattern.
     *           Example:
     *               // The InspectTemplate message originally only supported resource
     *               // names with organization, and project was added later.
     *               message InspectTemplate {
     *                 option (google.api.resource) = {
     *                   type: "dlp.googleapis.com/InspectTemplate"
     *                   pattern:
     *                   "organizations/{organization}/inspectTemplates/{inspect_template}"
     *                   pattern: "projects/{project}/inspectTemplates/{inspect_template}"
     *                   history: ORIGINALLY_SINGLE_PATTERN
     *                 };
     *               }
     *     @type string $plural
     *           The plural name used in the resource name and permission names, such as
     *           'projects' for the resource name of 'projects/{project}' and the permission
     *           name of 'cloudresourcemanager.googleapis.com/projects.get'. One exception
     *           to this is for Nested Collections that have stuttering names, as defined
     *           in [AIP-122](https://google.aip.dev/122#nested-collections), where the
     *           collection ID in the resource name pattern does not necessarily directly
     *           match the `plural` value.
     *           It is the same concept of the `plural` field in k8s CRD spec
     *           https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     *           Note: The plural form is required even for singleton resources. See
     *           https://aip.dev/156
     *     @type string $singular
     *           The same concept of the `singular` field in k8s CRD spec
     *           https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     *           Such as "project" for the `resourcemanager.googleapis.com/Project` type.
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $style
     *           Style flag(s) for this resource.
     *           These indicate that a resource is expected to conform to a given
     *           style. See the specific style flags for additional information.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Resource::initOnce();
        parent::__construct($data);
    }

    /**
     * The resource type. It must be in the format of
     * {service_name}/{resource_type_kind}. The `resource_type_kind` must be
     * singular and must not include version numbers.
     * Example: `storage.googleapis.com/Bucket`
     * The value of the resource_type_kind must follow the regular expression
     * /[A-Za-z][a-zA-Z0-9]+/. It should start with an upper case character and
     * should use PascalCase (UpperCamelCase). The maximum number of
     * characters allowed for the `resource_type_kind` is 100.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * The resource type. It must be in the format of
     * {service_name}/{resource_type_kind}. The `resource_type_kind` must be
     * singular and must not include version numbers.
     * Example: `storage.googleapis.com/Bucket`
     * The value of the resource_type_kind must follow the regular expression
     * /[A-Za-z][a-zA-Z0-9]+/. It should start with an upper case character and
     * should use PascalCase (UpperCamelCase). The maximum number of
     * characters allowed for the `resource_type_kind` is 100.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

    /**
     * Optional. The relative resource name pattern associated with this resource
     * type. The DNS prefix of the full resource name shouldn't be specified here.
     * The path pattern must follow the syntax, which aligns with HTTP binding
     * syntax:
     *     Template = Segment { "/" Segment } ;
     *     Segment = LITERAL | Variable ;
     *     Variable = "{" LITERAL "}" ;
     * Examples:
     *     - "projects/{project}/topics/{topic}"
     *     - "projects/{project}/knowledgeBases/{knowledge_base}"
     * The components in braces correspond to the IDs for each resource in the
     * hierarchy. It is expected that, if multiple patterns are provided,
     * the same component name (e.g. "project") refers to IDs of the same
     * type of resource.
     *
     * Generated from protobuf field <code>repeated string pattern = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Optional. The relative resource name pattern associated with this resource
     * type. The DNS prefix of the full resource name shouldn't be specified here.
     * The path pattern must follow the syntax, which aligns with HTTP binding
     * syntax:
     *     Template = Segment { "/" Segment } ;
     *     Segment = LITERAL | Variable ;
     *     Variable = "{" LITERAL "}" ;
     * Examples:
     *     - "projects/{project}/topics/{topic}"
     *     - "projects/{project}/knowledgeBases/{knowledge_base}"
     * The components in braces correspond to the IDs for each resource in the
     * hierarchy. It is expected that, if multiple patterns are provided,
     * the same component name (e.g. "project") refers to IDs of the same
     * type of resource.
     *
     * Generated from protobuf field <code>repeated string pattern = 2;</code>
     * @param array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPattern($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->pattern = $arr;

        return $this;
    }

    /**
     * Optional. The field on the resource that designates the resource name
     * field. If omitted, this is assumed to be "name".
     *
     * Generated from protobuf field <code>string name_field = 3;</code>
     * @return string
     */
    public function getNameField()
    {
        return $this->name_field;
    }

    /**
     * Optional. The field on the resource that designates the resource name
     * field. If omitted, this is assumed to be "name".
     *
     * Generated from protobuf field <code>string name_field = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setNameField($var)
    {
        GPBUtil::checkString($var, True);
        $this->name_field = $var;

        return $this;
    }

    /**
     * Optional. The historical or future-looking state of the resource pattern.
     * Example:
     *     // The InspectTemplate message originally only supported resource
     *     // names with organization, and project was added later.
     *     message InspectTemplate {
     *       option (google.api.resource) = {
     *         type: "dlp.googleapis.com/InspectTemplate"
     *         pattern:
     *         "organizations/{organization}/inspectTemplates/{inspect_template}"
     *         pattern: "projects/{project}/inspectTemplates/{inspect_template}"
     *         history: ORIGINALLY_SINGLE_PATTERN
     *       };
     *     }
     *
     * Generated from protobuf field <code>.google.api.ResourceDescriptor.History history = 4;</code>
     * @return int
     */
    public function getHistory()
    {
        return $this->history;
    }

    /**
     * Optional. The historical or future-looking state of the resource pattern.
     * Example:
     *     // The InspectTemplate message originally only supported resource
     *     // names with organization, and project was added later.
     *     message InspectTemplate {
     *       option (google.api.resource) = {
     *         type: "dlp.googleapis.com/InspectTemplate"
     *         pattern:
     *         "organizations/{organization}/inspectTemplates/{inspect_template}"
     *         pattern: "projects/{project}/inspectTemplates/{inspect_template}"
     *         history: ORIGINALLY_SINGLE_PATTERN
     *       };
     *     }
     *
     * Generated from protobuf field <code>.google.api.ResourceDescriptor.History history = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setHistory($var)
    {
        GPBUtil::checkEnum($var, \Google\Api\ResourceDescriptor\History::class);
        $this->history = $var;

        return $this;
    }

    /**
     * The plural name used in the resource name and permission names, such as
     * 'projects' for the resource name of 'projects/{project}' and the permission
     * name of 'cloudresourcemanager.googleapis.com/projects.get'. One exception
     * to this is for Nested Collections that have stuttering names, as defined
     * in [AIP-122](https://google.aip.dev/122#nested-collections), where the
     * collection ID in the resource name pattern does not necessarily directly
     * match the `plural` value.
     * It is the same concept of the `plural` field in k8s CRD spec
     * https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     * Note: The plural form is required even for singleton resources. See
     * https://aip.dev/156
     *
     * Generated from protobuf field <code>string plural = 5;</code>
     * @return string
     */
    public function getPlural()
    {
        return $this->plural;
    }

    /**
     * The plural name used in the resource name and permission names, such as
     * 'projects' for the resource name of 'projects/{project}' and the permission
     * name of 'cloudresourcemanager.googleapis.com/projects.get'. One exception
     * to this is for Nested Collections that have stuttering names, as defined
     * in [AIP-122](https://google.aip.dev/122#nested-collections), where the
     * collection ID in the resource name pattern does not necessarily directly
     * match the `plural` value.
     * It is the same concept of the `plural` field in k8s CRD spec
     * https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     * Note: The plural form is required even for singleton resources. See
     * https://aip.dev/156
     *
     * Generated from protobuf field <code>string plural = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setPlural($var)
    {
        GPBUtil::checkString($var, True);
        $this->plural = $var;

        return $this;
    }

    /**
     * The same concept of the `singular` field in k8s CRD spec
     * https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     * Such as "project" for the `resourcemanager.googleapis.com/Project` type.
     *
     * Generated from protobuf field <code>string singular = 6;</code>
     * @return string
     */
    public function getSingular()
    {
        return $this->singular;
    }

    /**
     * The same concept of the `singular` field in k8s CRD spec
     * https://kubernetes.io/docs/tasks/access-kubernetes-api/custom-resources/custom-resource-definitions/
     * Such as "project" for the `resourcemanager.googleapis.com/Project` type.
     *
     * Generated from protobuf field <code>string singular = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setSingular($var)
    {
        GPBUtil::checkString($var, True);
        $this->singular = $var;

        return $this;
    }

    /**
     * Style flag(s) for this resource.
     * These indicate that a resource is expected to conform to a given
     * style. See the specific style flags for additional information.
     *
     * Generated from protobuf field <code>repeated .google.api.ResourceDescriptor.Style style = 10;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getStyle()
    {
        return $this->style;
    }

    /**
     * Style flag(s) for this resource.
     * These indicate that a resource is expected to conform to a given
     * style. See the specific style flags for additional information.
     *
     * Generated from protobuf field <code>repeated .google.api.ResourceDescriptor.Style style = 10;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setStyle($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Api\ResourceDescriptor\Style::class);
        $this->style = $arr;

        return $this;
    }

}

