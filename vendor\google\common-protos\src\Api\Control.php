<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/control.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Selects and configures the service controller used by the service.
 * Example:
 *     control:
 *       environment: servicecontrol.googleapis.com
 *
 * Generated from protobuf message <code>google.api.Control</code>
 */
class Control extends \Google\Protobuf\Internal\Message
{
    /**
     * The service controller environment to use. If empty, no control plane
     * feature (like quota and billing) will be enabled. The recommended value for
     * most services is servicecontrol.googleapis.com
     *
     * Generated from protobuf field <code>string environment = 1;</code>
     */
    protected $environment = '';
    /**
     * Defines policies applying to the API methods of the service.
     *
     * Generated from protobuf field <code>repeated .google.api.MethodPolicy method_policies = 4;</code>
     */
    private $method_policies;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $environment
     *           The service controller environment to use. If empty, no control plane
     *           feature (like quota and billing) will be enabled. The recommended value for
     *           most services is servicecontrol.googleapis.com
     *     @type array<\Google\Api\MethodPolicy>|\Google\Protobuf\Internal\RepeatedField $method_policies
     *           Defines policies applying to the API methods of the service.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Control::initOnce();
        parent::__construct($data);
    }

    /**
     * The service controller environment to use. If empty, no control plane
     * feature (like quota and billing) will be enabled. The recommended value for
     * most services is servicecontrol.googleapis.com
     *
     * Generated from protobuf field <code>string environment = 1;</code>
     * @return string
     */
    public function getEnvironment()
    {
        return $this->environment;
    }

    /**
     * The service controller environment to use. If empty, no control plane
     * feature (like quota and billing) will be enabled. The recommended value for
     * most services is servicecontrol.googleapis.com
     *
     * Generated from protobuf field <code>string environment = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setEnvironment($var)
    {
        GPBUtil::checkString($var, True);
        $this->environment = $var;

        return $this;
    }

    /**
     * Defines policies applying to the API methods of the service.
     *
     * Generated from protobuf field <code>repeated .google.api.MethodPolicy method_policies = 4;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getMethodPolicies()
    {
        return $this->method_policies;
    }

    /**
     * Defines policies applying to the API methods of the service.
     *
     * Generated from protobuf field <code>repeated .google.api.MethodPolicy method_policies = 4;</code>
     * @param array<\Google\Api\MethodPolicy>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMethodPolicies($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Api\MethodPolicy::class);
        $this->method_policies = $arr;

        return $this;
    }

}

