# PSR Http Message Util

This repository holds utility classes and constants to facilitate common
operations of [PSR-7](https://www.php-fig.org/psr/psr-7/); the primary purpose is
to provide constants for referring to request methods, response status codes and
messages, and potentially common headers.

Implementation of PSR-7 interfaces is **not** within the scope of this package.

## Installation

Install by adding the package as a [Composer](https://getcomposer.org)
requirement:

```bash
$ composer require fig/http-message-util
```
