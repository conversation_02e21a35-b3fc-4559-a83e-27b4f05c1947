<?php
defined('ABSPATH') or die('No script kiddies please!');
require_once __DIR__ . '/../vendor/autoload.php'; // Adjust path if needed

use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use <PERSON>reait\Firebase\Messaging\Notification;

/**
 * Get the path to the service account JSON file.
 *
 * @return string The path to the service account file.
 */
function q_get_service_account_path()
{
    // Get Firebase config from WordPress settings
    $firebase_config = get_option('q_firebase_config');

    if (empty($firebase_config)) {
        error_log("ERROR: Firebase configuration not found in WordPress settings");
        return false;
    }

    // Validate JSON
    $config = json_decode($firebase_config, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("ERROR: Firebase configuration is not valid JSON");
        return false;
    }

    // Create temporary file with config
    $temp_file = tempnam(sys_get_temp_dir(), 'firebase_config_');
    if ($temp_file === false) {
        error_log("ERROR: Could not create temporary file for Firebase config");
        return false;
    }

    file_put_contents($temp_file, $firebase_config);
    return $temp_file;
}

/**
 * Get the notification icon URL.
 *
 * @return string The URL of the notification icon.
 */
function q_get_notification_icon()
{
    // Try to get site icon URL
    $site_icon = get_site_icon_url();

    return esc_url($site_icon); // Sanitize the URL
}

function q_check_rate_limits($token, $type = 'notification')
{
    // Rate limit keys with different scopes
    $rate_limit_keys = [
        'hourly' => 'q_rate_' . md5($token) . '_hourly',
        'daily' => 'q_rate_' . md5($token) . '_daily'
    ];

    // Get current counts
    $hourly_count = (int) get_transient($rate_limit_keys['hourly']) ?: 0;
    $daily_count = (int) get_transient($rate_limit_keys['daily']) ?: 0;

    // Define limits
    $limits = [
        'notification' => ['hourly' => 30, 'daily' => 100],
        'subscription' => ['hourly' => 5, 'daily' => 10]
    ];

    // Check limits
    if (
        $hourly_count >= $limits[$type]['hourly'] ||
        $daily_count >= $limits[$type]['daily']
    ) {
        error_log("Rate limit reached for token: " . substr($token, 0, 10) . "...");
        return false;
    }

    // Increment counters
    set_transient($rate_limit_keys['hourly'], $hourly_count + 1, HOUR_IN_SECONDS);
    set_transient($rate_limit_keys['daily'], $daily_count + 1, DAY_IN_SECONDS);

    return true;
}

/**
 * Get cached Firebase Messaging instance
 *
 * @return Messaging|null Firebase Messaging instance or null on failure
 */
function q_get_firebase_messaging()
{
    static $messaging = null;

    if ($messaging !== null) {
        return $messaging;
    }

    $service_account = get_option('q_firebase_service_account');
    if (empty($service_account)) {
        error_log("ERROR: Firebase service account not found in settings");
        return null;
    }

    try {
        $factory = (new Kreait\Firebase\Factory)
            ->withServiceAccount($service_account);
        $messaging = $factory->createMessaging();
        return $messaging;
    } catch (Exception $e) {
        error_log("Failed to initialize Firebase Messaging: " . $e->getMessage());
        return null;
    }
}

/**
 * Send a push notification to a specific token.
 *
 * @param string $token The recipient's device token.
 * @param string $title The title of the notification.
 * @param string $message The message of the notification.
 * @param string $image Optional image for the notification.
 * @param bool $debug Optional flag to enable debug logging.
 * @return bool True if the notification was sent successfully, false otherwise.
 */
function q_send_push_notification($token, $title, $message, $image = '', $debug = false, $type = 'info', $additional_data = [])
{
    if (!q_check_rate_limits($token, 'notification')) {
        return false;
    }

    try {
        // Validate inputs
        if (empty($token) || empty($title) || empty($message)) {
            error_log("ERROR: Missing required parameters in q_send_push_notification");
            return false;
        }

        // Check for duplicate messages
        $dedup_key = 'q_push_dedup_' . md5($token . $title . $message);
        if (get_transient($dedup_key)) {
            error_log("Duplicate notification prevented for token: " . substr($token, 0, 10) . "...");
            return false;
        }

        $messaging = q_get_firebase_messaging();
        if (!$messaging) {
            return false;
        }

        // Sanitize inputs
        $title = sanitize_text_field($title);
        $message = sanitize_textarea_field($message);

        // Get default icon for the icon field only (not for image)
        $icon = q_get_notification_icon();

        // Use provided image or fallback to null (don't use icon as fallback)
        $notification_image = !empty($image) ? esc_url($image) : null;

        $redirect_url = site_url() . '/app';

        // Generate a unique message ID for deduplication
        $message_id = md5($token . $title . $message . time());

        // Generate a unique notification ID
        $notification_id = uniqid('notify_', true);

        // Include form_id in the notification data if available
        $form_id = isset($additional_data['form_id']) ? $additional_data['form_id'] : null;

        // Prepare data payload with all notification information
        $data = [
            'title' => $title,
            'body' => $message,
            'image' => $notification_image,
            'icon' => $icon,
            'timestamp' => (string) time(),
            'message_id' => $message_id,
            'notification_id' => $notification_id,
            'form_id' => $form_id ? (string) $form_id : '',
            'click_action' => $redirect_url,
            'badge' => $icon // Use icon as badge for notification
        ];

        // Add retrigger information if provided in additional_data
        if (isset($additional_data['enable_retrigger']) && $additional_data['enable_retrigger']) {
            $data['enable_retrigger'] = 'true';
            $data['retrigger_delay'] = (string) (isset($additional_data['retrigger_delay']) ?
                intval($additional_data['retrigger_delay']) : 24);
            $data['retrigger_max_attempts'] = (string) (isset($additional_data['retrigger_max_attempts']) ?
                intval($additional_data['retrigger_max_attempts']) : 3);
            $data['retrigger_attempt'] = (string) (isset($additional_data['retrigger_attempt']) ?
                intval($additional_data['retrigger_attempt']) : 0);
            $data['action_id'] = (string) (isset($additional_data['action_id']) ?
                intval($additional_data['action_id']) : 0);

            // Store retrigger metadata
            if (isset($additional_data['is_retrigger']) && $additional_data['is_retrigger']) {
                $data['is_retrigger'] = 'true';
                $data['original_notification_id'] = $additional_data['original_notification_id'];
                $data['last_retrigger_time'] = (string) (isset($additional_data['last_retrigger_time']) ?
                    intval($additional_data['last_retrigger_time']) : time());
            }
        }

        // Structure the message using data payload instead of notification payload
        $payload = [
            'data' => $data,
            'token' => $token,
            // Set high priority for consistent delivery
            'android' => [
                'priority' => 'high',
            ],
            'apns' => [
                'headers' => [
                    'apns-priority' => '10'
                ],
                'payload' => [
                    'aps' => [
                        'badge' => 1, // Set badge count for iOS
                        'sound' => 'default',
                        'content-available' => 1
                    ]
                ]
            ]
        ];

        if ($debug) {
            error_log("Sending message payload: " . print_r($payload, true));
        }

        $response = $messaging->send(CloudMessage::fromArray($payload));

        // Set deduplication for 5 minutes
        set_transient($dedup_key, true, 5 * 60);

        if ($debug) {
            error_log("Firebase Response: " . print_r($response, true));
        }

        // Track notification if sent successfully
        if (!empty($response) && is_string($response['name'])) {
            // Prepare tracking data
            $tracking_data = [
                'title' => $title,
                'message' => $message,
                'token' => substr($token, 0, 10) . '...',
                'type' => $type
            ];

            // Add retrigger information to tracking data if available
            if (isset($payload['data']['enable_retrigger']) && $payload['data']['enable_retrigger'] === 'true') {
                $tracking_data['enable_retrigger'] = true;
                $tracking_data['retrigger_delay'] = $payload['data']['retrigger_delay'];
                $tracking_data['retrigger_max_attempts'] = $payload['data']['retrigger_max_attempts'];
                $tracking_data['retrigger_attempt'] = $payload['data']['retrigger_attempt'];
                $tracking_data['action_id'] = $payload['data']['action_id'];

                // Include retrigger metadata if this is a retrigger notification
                if (isset($payload['data']['is_retrigger']) && $payload['data']['is_retrigger'] === 'true') {
                    $tracking_data['is_retrigger'] = true;
                    $tracking_data['original_notification_id'] = $payload['data']['original_notification_id'];
                    $tracking_data['last_retrigger_time'] = $payload['data']['last_retrigger_time'];
                }
            }

            // Track the sent notification
            q_track_notification($notification_id, 'sent', $tracking_data, $form_id);

            return true;
        } else {
            error_log("Firebase notification failed: Invalid response format - " . print_r($response, true));
            return false;
        }

    } catch (Exception $e) {
        error_log("ERROR in q_send_push_notification: " . $e->getMessage());
        if ($debug) {
            error_log("Stack trace: " . $e->getTraceAsString());
        }
        return false;
    }
}

/**
 * Send multiple push notifications in batch
 *
 * @param array $notifications Array of notification data
 * @param bool $debug Optional flag to enable debug logging
 * @return array Array of results with token as key and success status as value
 */
function q_send_batch_notifications($notifications, $debug = false)
{
    $service_account_path = q_get_service_account_path();
    if (!$service_account_path) {
        return [];
    }

    // Initialize Firebase once for all notifications
    static $messaging = null;
    if ($messaging === null) {
        $factory = (new Kreait\Firebase\Factory)
            ->withServiceAccount($service_account_path);
        $messaging = $factory->createMessaging();
    }

    $results = [];
    $batch = [];
    $batch_size = 500; // Firebase's maximum batch size

    foreach ($notifications as $notification) {
        // Skip if rate limit reached
        if (!q_check_rate_limits($notification['token'], 'notification')) {
            $results[$notification['token']] = false;
            continue;
        }

        // Check for duplicate messages
        $dedup_key = 'q_push_dedup_' . md5($notification['token'] . $notification['title'] . $notification['message']);
        if (get_transient($dedup_key)) {
            $results[$notification['token']] = false;
            continue;
        }

        // Prepare message payload
        $message_id = md5($notification['token'] . $notification['title'] . $notification['message'] . time());
        $notification_id = uniqid('notify_', true);

        $payload = [
            'data' => [
                'title' => sanitize_text_field($notification['title']),
                'body' => sanitize_textarea_field($notification['message']),
                'image' => !empty($notification['image']) ? esc_url($notification['image']) : null,
                'icon' => q_get_notification_icon(),
                'timestamp' => (string) time(),
                'message_id' => $message_id,
                'notification_id' => $notification_id,
                'form_id' => isset($notification['form_id']) ? (string) $notification['form_id'] : '',
                'click_action' => site_url() . '/app',
                'badge' => q_get_notification_icon() // Use icon as badge
            ],
            'token' => $notification['token'],
            'android' => ['priority' => 'high'],
            'apns' => [
                'headers' => ['apns-priority' => '10'],
                'payload' => [
                    'aps' => [
                        'badge' => 1, // Set badge count for iOS
                        'sound' => 'default',
                        'content-available' => 1
                    ]
                ]
            ]
        ];

        $batch[] = CloudMessage::fromArray($payload);
        set_transient($dedup_key, true, 5 * 60);

        // Process batch when it reaches maximum size
        if (count($batch) >= $batch_size) {
            $batch_results = q_process_notification_batch($messaging, $batch, $debug);
            $results = array_merge($results, $batch_results);
            $batch = [];
        }
    }

    // Process remaining notifications
    if (!empty($batch)) {
        $batch_results = q_process_notification_batch($messaging, $batch, $debug);
        $results = array_merge($results, $batch_results);
    }

    return $results;
}

/**
 * Process a batch of notifications
 *
 * @param Messaging $messaging Firebase Messaging instance
 * @param array $batch Array of CloudMessage objects
 * @param bool $debug Debug flag
 * @return array Results array
 */
function q_process_notification_batch($messaging, $batch, $debug)
{
    $results = [];

    try {
        $response = $messaging->sendAll($batch);

        if ($debug) {
            error_log("Batch sending response: " . print_r($response, true));
        }

        foreach ($response->getItems() as $index => $result) {
            $token = $batch[$index]->getToken();
            $success = $result->isSuccess();
            $results[$token] = $success;

            if ($success) {
                // Track successful notification
                q_track_notification(
                    $batch[$index]->getData()['notification_id'],
                    'sent',
                    [
                        'title' => $batch[$index]->getData()['title'],
                        'message' => $batch[$index]->getData()['body'],
                        'token' => substr($token, 0, 10) . '...',
                        'type' => 'info'
                    ],
                    $batch[$index]->getData()['form_id']
                );
            } else {
                // Handle failure
                error_log("Failed to send notification to token: " . substr($token, 0, 10) . "... Error: " . $result->error()->getMessage());
            }
        }
    } catch (Exception $e) {
        error_log("Batch sending failed: " . $e->getMessage());
        foreach ($batch as $message) {
            $results[$message->getToken()] = false;
        }
    }

    return $results;
}

/**
 * Retry failed notification with exponential backoff
 *
 * @param array $notification Notification data
 * @param int $maxAttempts Maximum number of retry attempts
 * @param int $attempt Current attempt number
 * @return bool Success status
 */
function q_retry_failed_notification($notification, $maxAttempts = 3, $attempt = 1)
{
    if ($attempt > $maxAttempts) {
        error_log("Max retry attempts reached for notification to token: " . substr($notification['token'], 0, 10) . "...");
        return false;
    }

    // Exponential backoff delay
    $delay = pow(2, $attempt - 1);
    sleep($delay);

    try {
        $messaging = q_get_firebase_messaging();
        if (!$messaging) {
            throw new Exception("Failed to initialize Firebase Messaging");
        }

        $payload = [
            'data' => [
                'title' => $notification['title'],
                'body' => $notification['message'],
                'image' => $notification['image'] ?? null,
                'icon' => q_get_notification_icon(),
                'timestamp' => (string) time(),
                'message_id' => $notification['message_id'],
                'notification_id' => $notification['notification_id'],
                'form_id' => $notification['form_id'] ?? '',
                'click_action' => site_url() . '/app',
                'retry_attempt' => (string) $attempt,
                'badge' => q_get_notification_icon() // Use icon as badge
            ],
            'token' => $notification['token'],
            'android' => ['priority' => 'high'],
            'apns' => [
                'headers' => ['apns-priority' => '10'],
                'payload' => [
                    'aps' => [
                        'badge' => 1, // Set badge count for iOS
                        'sound' => 'default',
                        'content-available' => 1
                    ]
                ]
            ]
        ];

        $response = $messaging->send(CloudMessage::fromArray($payload));
        return !empty($response) && is_string($response['name']);
    } catch (Exception $e) {
        error_log("Retry attempt $attempt failed: " . $e->getMessage());
        return q_retry_failed_notification($notification, $maxAttempts, $attempt + 1);
    }
}
