<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\GPBWire;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\InputStream;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>google.protobuf.EnumValueOptions</code>
 */
class EnumValueOptions extends \Google\Protobuf\Internal\Message
{
    /**
     * Is this enum value deprecated?
     * Depending on the target platform, this can emit Deprecated annotations
     * for the enum value, or it will be completely ignored; in the very least,
     * this is a formalization for deprecating enum values.
     *
     * Generated from protobuf field <code>optional bool deprecated = 1 [default = false];</code>
     */
    protected $deprecated = null;
    /**
     * Any features defined in the specific edition.
     * WARNING: This field should only be used by protobuf plugins or special
     * cases like the proto compiler. Other uses are discouraged and
     * developers should rely on the protoreflect APIs for their client language.
     *
     * Generated from protobuf field <code>optional .google.protobuf.FeatureSet features = 2;</code>
     */
    protected $features = null;
    /**
     * Indicate that fields annotated with this enum value should not be printed
     * out when using debug formats, e.g. when the field contains sensitive
     * credentials.
     *
     * Generated from protobuf field <code>optional bool debug_redact = 3 [default = false];</code>
     */
    protected $debug_redact = null;
    /**
     * Information about the support window of a feature value.
     *
     * Generated from protobuf field <code>optional .google.protobuf.FieldOptions.FeatureSupport feature_support = 4;</code>
     */
    protected $feature_support = null;
    /**
     * The parser stores options it doesn't recognize here. See above.
     *
     * Generated from protobuf field <code>repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;</code>
     */
    private $uninterpreted_option;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type bool $deprecated
     *           Is this enum value deprecated?
     *           Depending on the target platform, this can emit Deprecated annotations
     *           for the enum value, or it will be completely ignored; in the very least,
     *           this is a formalization for deprecating enum values.
     *     @type \Google\Protobuf\Internal\FeatureSet $features
     *           Any features defined in the specific edition.
     *           WARNING: This field should only be used by protobuf plugins or special
     *           cases like the proto compiler. Other uses are discouraged and
     *           developers should rely on the protoreflect APIs for their client language.
     *     @type bool $debug_redact
     *           Indicate that fields annotated with this enum value should not be printed
     *           out when using debug formats, e.g. when the field contains sensitive
     *           credentials.
     *     @type \Google\Protobuf\Internal\FieldOptions\FeatureSupport $feature_support
     *           Information about the support window of a feature value.
     *     @type array<\Google\Protobuf\Internal\UninterpretedOption>|\Google\Protobuf\Internal\RepeatedField $uninterpreted_option
     *           The parser stores options it doesn't recognize here. See above.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Protobuf\Internal\Descriptor::initOnce();
        parent::__construct($data);
    }

    /**
     * Is this enum value deprecated?
     * Depending on the target platform, this can emit Deprecated annotations
     * for the enum value, or it will be completely ignored; in the very least,
     * this is a formalization for deprecating enum values.
     *
     * Generated from protobuf field <code>optional bool deprecated = 1 [default = false];</code>
     * @return bool
     */
    public function getDeprecated()
    {
        return isset($this->deprecated) ? $this->deprecated : false;
    }

    public function hasDeprecated()
    {
        return isset($this->deprecated);
    }

    public function clearDeprecated()
    {
        unset($this->deprecated);
    }

    /**
     * Is this enum value deprecated?
     * Depending on the target platform, this can emit Deprecated annotations
     * for the enum value, or it will be completely ignored; in the very least,
     * this is a formalization for deprecating enum values.
     *
     * Generated from protobuf field <code>optional bool deprecated = 1 [default = false];</code>
     * @param bool $var
     * @return $this
     */
    public function setDeprecated($var)
    {
        GPBUtil::checkBool($var);
        $this->deprecated = $var;

        return $this;
    }

    /**
     * Any features defined in the specific edition.
     * WARNING: This field should only be used by protobuf plugins or special
     * cases like the proto compiler. Other uses are discouraged and
     * developers should rely on the protoreflect APIs for their client language.
     *
     * Generated from protobuf field <code>optional .google.protobuf.FeatureSet features = 2;</code>
     * @return \Google\Protobuf\Internal\FeatureSet|null
     */
    public function getFeatures()
    {
        return $this->features;
    }

    public function hasFeatures()
    {
        return isset($this->features);
    }

    public function clearFeatures()
    {
        unset($this->features);
    }

    /**
     * Any features defined in the specific edition.
     * WARNING: This field should only be used by protobuf plugins or special
     * cases like the proto compiler. Other uses are discouraged and
     * developers should rely on the protoreflect APIs for their client language.
     *
     * Generated from protobuf field <code>optional .google.protobuf.FeatureSet features = 2;</code>
     * @param \Google\Protobuf\Internal\FeatureSet $var
     * @return $this
     */
    public function setFeatures($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Internal\FeatureSet::class);
        $this->features = $var;

        return $this;
    }

    /**
     * Indicate that fields annotated with this enum value should not be printed
     * out when using debug formats, e.g. when the field contains sensitive
     * credentials.
     *
     * Generated from protobuf field <code>optional bool debug_redact = 3 [default = false];</code>
     * @return bool
     */
    public function getDebugRedact()
    {
        return isset($this->debug_redact) ? $this->debug_redact : false;
    }

    public function hasDebugRedact()
    {
        return isset($this->debug_redact);
    }

    public function clearDebugRedact()
    {
        unset($this->debug_redact);
    }

    /**
     * Indicate that fields annotated with this enum value should not be printed
     * out when using debug formats, e.g. when the field contains sensitive
     * credentials.
     *
     * Generated from protobuf field <code>optional bool debug_redact = 3 [default = false];</code>
     * @param bool $var
     * @return $this
     */
    public function setDebugRedact($var)
    {
        GPBUtil::checkBool($var);
        $this->debug_redact = $var;

        return $this;
    }

    /**
     * Information about the support window of a feature value.
     *
     * Generated from protobuf field <code>optional .google.protobuf.FieldOptions.FeatureSupport feature_support = 4;</code>
     * @return \Google\Protobuf\Internal\FieldOptions\FeatureSupport|null
     */
    public function getFeatureSupport()
    {
        return $this->feature_support;
    }

    public function hasFeatureSupport()
    {
        return isset($this->feature_support);
    }

    public function clearFeatureSupport()
    {
        unset($this->feature_support);
    }

    /**
     * Information about the support window of a feature value.
     *
     * Generated from protobuf field <code>optional .google.protobuf.FieldOptions.FeatureSupport feature_support = 4;</code>
     * @param \Google\Protobuf\Internal\FieldOptions\FeatureSupport $var
     * @return $this
     */
    public function setFeatureSupport($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Internal\FieldOptions\FeatureSupport::class);
        $this->feature_support = $var;

        return $this;
    }

    /**
     * The parser stores options it doesn't recognize here. See above.
     *
     * Generated from protobuf field <code>repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUninterpretedOption()
    {
        return $this->uninterpreted_option;
    }

    /**
     * The parser stores options it doesn't recognize here. See above.
     *
     * Generated from protobuf field <code>repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;</code>
     * @param array<\Google\Protobuf\Internal\UninterpretedOption>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUninterpretedOption($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Protobuf\Internal\UninterpretedOption::class);
        $this->uninterpreted_option = $arr;

        return $this;
    }

}

