<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Service Worker Fix Test</h1>
    
    <div class="test-section">
        <h2>Service Worker Status</h2>
        <div id="sw-status" class="status">
            Checking service worker...
        </div>
        <button class="test-button" onclick="checkServiceWorker()">
            🔍 Check Service Worker
        </button>
        <button class="test-button" onclick="updateServiceWorker()">
            🔄 Update Service Worker
        </button>
    </div>

    <div class="test-section">
        <h2>Cache Strategy Tests</h2>
        <p>Test the fixed caching strategies to ensure no Response cloning errors:</p>
        
        <button class="test-button" onclick="testCacheFirst()">
            📦 Test Cache First
        </button>
        
        <button class="test-button" onclick="testNetworkFirst()">
            🌐 Test Network First
        </button>
        
        <button class="test-button" onclick="testStaleWhileRevalidate()">
            ⚡ Test Stale While Revalidate
        </button>
        
        <button class="test-button" onclick="testOfflinePage()">
            📡 Test Offline Page
        </button>
    </div>

    <div class="test-section">
        <h2>Console Log</h2>
        <div id="console-log" class="log">
            Waiting for test results...
        </div>
        <button class="test-button" onclick="clearLog()">
            🗑️ Clear Log
        </button>
    </div>

    <script>
        let logElement;
        
        document.addEventListener('DOMContentLoaded', function() {
            logElement = document.getElementById('console-log');
            checkServiceWorker();
            
            // Listen for service worker messages
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.addEventListener('message', function(event) {
                    log('SW Message: ' + JSON.stringify(event.data));
                });
            }
        });

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logElement.innerHTML += logEntry + '<br>';
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLog() {
            logElement.innerHTML = 'Log cleared...<br>';
        }

        async function checkServiceWorker() {
            const statusEl = document.getElementById('sw-status');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        statusEl.textContent = `Service Worker registered: ${registration.scope}`;
                        statusEl.className = 'status';
                        log('✅ Service Worker is registered and active');
                        
                        if (registration.waiting) {
                            log('⏳ Service Worker update is waiting');
                        }
                        
                        if (registration.installing) {
                            log('🔄 Service Worker is installing');
                        }
                        
                        if (registration.active) {
                            log('✅ Service Worker is active');
                        }
                    } else {
                        statusEl.textContent = 'No Service Worker registered';
                        statusEl.className = 'status error';
                        log('❌ No Service Worker registered');
                    }
                } catch (error) {
                    statusEl.textContent = `Error: ${error.message}`;
                    statusEl.className = 'status error';
                    log('❌ Error checking Service Worker: ' + error.message);
                }
            } else {
                statusEl.textContent = 'Service Worker not supported';
                statusEl.className = 'status error';
                log('❌ Service Worker not supported in this browser');
            }
        }

        async function updateServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        log('🔄 Updating Service Worker...');
                        await registration.update();
                        log('✅ Service Worker update initiated');
                    } else {
                        log('❌ No Service Worker to update');
                    }
                } catch (error) {
                    log('❌ Error updating Service Worker: ' + error.message);
                }
            }
        }

        async function testCacheFirst() {
            log('🧪 Testing Cache First strategy...');
            try {
                const response = await fetch('/wp-content/plugins/q-pusher-q-pwa/includes/css/pwa-styles.css?test=cache-first&t=' + Date.now());
                if (response.ok) {
                    log('✅ Cache First test successful');
                } else {
                    log('⚠️ Cache First test returned: ' + response.status);
                }
            } catch (error) {
                log('❌ Cache First test failed: ' + error.message);
            }
        }

        async function testNetworkFirst() {
            log('🧪 Testing Network First strategy...');
            try {
                const response = await fetch('/wp-admin/admin-ajax.php?action=test&test=network-first&t=' + Date.now());
                log('✅ Network First test completed (status: ' + response.status + ')');
            } catch (error) {
                log('❌ Network First test failed: ' + error.message);
            }
        }

        async function testStaleWhileRevalidate() {
            log('🧪 Testing Stale While Revalidate strategy...');
            try {
                // Test with a page request that should use stale-while-revalidate
                const response = await fetch('/?test=stale-while-revalidate&t=' + Date.now());
                if (response.ok) {
                    log('✅ Stale While Revalidate test successful');
                } else {
                    log('⚠️ Stale While Revalidate test returned: ' + response.status);
                }
            } catch (error) {
                log('❌ Stale While Revalidate test failed: ' + error.message);
            }
        }

        async function testOfflinePage() {
            log('🧪 Testing Offline Page...');
            try {
                const response = await fetch('/offline.html?t=' + Date.now());
                if (response.ok) {
                    log('✅ Offline page is accessible');
                } else {
                    log('⚠️ Offline page returned: ' + response.status);
                }
            } catch (error) {
                log('❌ Offline page test failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
