<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/date.proto

namespace Google\Type;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Represents a whole or partial calendar date, such as a birthday. The time of
 * day and time zone are either specified elsewhere or are insignificant. The
 * date is relative to the Gregorian Calendar. This can represent one of the
 * following:
 * * A full date, with non-zero year, month, and day values
 * * A month and day value, with a zero year, such as an anniversary
 * * A year on its own, with zero month and day values
 * * A year and month value, with a zero day, such as a credit card expiration
 * date
 * Related types are [google.type.TimeOfDay][google.type.TimeOfDay] and
 * `google.protobuf.Timestamp`.
 *
 * Generated from protobuf message <code>google.type.Date</code>
 */
class Date extends \Google\Protobuf\Internal\Message
{
    /**
     * Year of the date. Must be from 1 to 9999, or 0 to specify a date without
     * a year.
     *
     * Generated from protobuf field <code>int32 year = 1;</code>
     */
    protected $year = 0;
    /**
     * Month of a year. Must be from 1 to 12, or 0 to specify a year without a
     * month and day.
     *
     * Generated from protobuf field <code>int32 month = 2;</code>
     */
    protected $month = 0;
    /**
     * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
     * to specify a year by itself or a year and month where the day isn't
     * significant.
     *
     * Generated from protobuf field <code>int32 day = 3;</code>
     */
    protected $day = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $year
     *           Year of the date. Must be from 1 to 9999, or 0 to specify a date without
     *           a year.
     *     @type int $month
     *           Month of a year. Must be from 1 to 12, or 0 to specify a year without a
     *           month and day.
     *     @type int $day
     *           Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
     *           to specify a year by itself or a year and month where the day isn't
     *           significant.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Type\Date::initOnce();
        parent::__construct($data);
    }

    /**
     * Year of the date. Must be from 1 to 9999, or 0 to specify a date without
     * a year.
     *
     * Generated from protobuf field <code>int32 year = 1;</code>
     * @return int
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * Year of the date. Must be from 1 to 9999, or 0 to specify a date without
     * a year.
     *
     * Generated from protobuf field <code>int32 year = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setYear($var)
    {
        GPBUtil::checkInt32($var);
        $this->year = $var;

        return $this;
    }

    /**
     * Month of a year. Must be from 1 to 12, or 0 to specify a year without a
     * month and day.
     *
     * Generated from protobuf field <code>int32 month = 2;</code>
     * @return int
     */
    public function getMonth()
    {
        return $this->month;
    }

    /**
     * Month of a year. Must be from 1 to 12, or 0 to specify a year without a
     * month and day.
     *
     * Generated from protobuf field <code>int32 month = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setMonth($var)
    {
        GPBUtil::checkInt32($var);
        $this->month = $var;

        return $this;
    }

    /**
     * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
     * to specify a year by itself or a year and month where the day isn't
     * significant.
     *
     * Generated from protobuf field <code>int32 day = 3;</code>
     * @return int
     */
    public function getDay()
    {
        return $this->day;
    }

    /**
     * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
     * to specify a year by itself or a year and month where the day isn't
     * significant.
     *
     * Generated from protobuf field <code>int32 day = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setDay($var)
    {
        GPBUtil::checkInt32($var);
        $this->day = $var;

        return $this;
    }

}

