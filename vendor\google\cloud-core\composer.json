{"name": "google/cloud-core", "description": "Google Cloud PHP shared dependency, providing functionality useful to all components.", "license": "Apache-2.0", "minimum-stability": "stable", "require": {"php": "^8.0", "rize/uri-template": "~0.3", "google/auth": "^1.34", "guzzlehttp/guzzle": "^6.5.8|^7.4.4", "guzzlehttp/promises": "^1.4||^2.0", "guzzlehttp/psr7": "^2.6", "monolog/monolog": "^2.9|^3.0", "psr/http-message": "^1.0|^2.0", "google/gax": "^1.36.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "phpspec/prophecy-phpunit": "^2.0", "squizlabs/php_codesniffer": "2.*", "phpdocumentor/reflection": "^5.3.3||^6.0", "phpdocumentor/reflection-docblock": "^5.3", "erusev/parsedown": "^1.6", "opis/closure": "^3", "google/cloud-common-protos": "~0.5"}, "suggest": {"opis/closure": "May be used to serialize closures to process jobs in the batch daemon. Please require version ^3.", "symfony/lock": "Required for the Spanner cached based session pool. Please require the following commit: 3.3.x-dev#1ba6ac9"}, "extra": {"component": {"id": "cloud-core", "target": "googleapis/google-cloud-php-core.git", "path": "Core", "entry": "src/ServiceBuilder.php"}}, "bin": ["bin/google-cloud-batch"], "autoload": {"psr-4": {"Google\\Cloud\\Core\\": "src"}}, "autoload-dev": {"psr-4": {"Google\\Cloud\\Core\\Tests\\": "tests"}}}