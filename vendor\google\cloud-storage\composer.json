{"name": "google/cloud-storage", "description": "Cloud Storage Client for PHP", "license": "Apache-2.0", "minimum-stability": "stable", "require": {"php": "^8.0", "google/cloud-core": "^1.57", "ramsey/uuid": "^4.2.3"}, "require-dev": {"phpunit/phpunit": "^9.0", "phpspec/prophecy-phpunit": "^2.0", "squizlabs/php_codesniffer": "2.*", "phpdocumentor/reflection": "^5.3.3||^6.0", "phpdocumentor/reflection-docblock": "^5.3", "erusev/parsedown": "^1.6", "phpseclib/phpseclib": "^2.0||^3.0", "google/cloud-pubsub": "^2.0"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for creating signed Cloud Storage URLs. Please require version ^2.", "google/cloud-pubsub": "May be used to register a topic to receive bucket notifications."}, "extra": {"component": {"id": "cloud-storage", "target": "googleapis/google-cloud-php-storage.git", "path": "Storage", "entry": "src/StorageClient.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\Storage\\": "src"}}, "autoload-dev": {"psr-4": {"Google\\Cloud\\Storage\\Tests\\": "tests"}}}