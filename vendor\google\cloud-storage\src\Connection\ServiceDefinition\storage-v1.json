{"kind": "discovery#restDescription", "version": "v1", "id": "storage:v1", "rootUrl": "https://storage.googleapis.com/", "mtlsRootUrl": "https://storage.mtls.googleapis.com/", "baseUrl": "https://storage.googleapis.com/storage/v1/", "basePath": "/storage/v1/", "servicePath": "storage/v1/", "batchPath": "batch/storage/v1", "discoveryVersion": "v1", "name": "storage", "title": "Cloud Storage JSON API", "description": "Stores and retrieves potentially large, immutable data objects.", "ownerDomain": "google.com", "ownerName": "Google", "icons": {"x16": "https://www.google.com/images/icons/product/cloud_storage-16.png", "x32": "https://www.google.com/images/icons/product/cloud_storage-32.png"}, "documentationLink": "https://developers.google.com/storage/docs/json_api/", "labels": ["labs"], "endpoints": [{"endpointUrl": "https://storage.me-central2.rep.googleapis.com/", "location": "me-central2", "description": "Regional Endpoint"}], "protocol": "rest", "parameters": {"alt": {"type": "string", "description": "Data format for the response.", "default": "json", "enum": ["json"], "enumDescriptions": ["Responses with Content-Type of application/json"], "location": "query"}, "fields": {"type": "string", "description": "Selector specifying which fields to include in a partial response.", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true", "location": "query"}, "quotaUser": {"type": "string", "description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "location": "query"}, "userIp": {"type": "string", "description": "Deprecated. Please use quotaUser instead.", "location": "query"}, "uploadType": {"type": "string", "description": "Upload protocol for media (e.g. \"media\", \"multipart\", \"resumable\").", "location": "query"}}, "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud Platform services"}, "https://www.googleapis.com/auth/devstorage.full_control": {"description": "Manage your data and permissions in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_only": {"description": "View your data in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_write": {"description": "Manage your data in Google Cloud Storage"}}}}, "schemas": {"Bucket": {"id": "Bucket", "type": "object", "description": "A bucket.", "properties": {"acl": {"type": "array", "description": "Access controls on the bucket.", "items": {"$ref": "BucketAccessControl"}, "annotations": {"required": ["storage.buckets.update"]}}, "billing": {"type": "object", "description": "The bucket's billing configuration.", "properties": {"requesterPays": {"type": "boolean", "description": "When set to true, Requester Pays is enabled for this bucket."}}}, "cors": {"type": "array", "description": "The bucket's Cross-Origin Resource Sharing (CORS) configuration.", "items": {"type": "object", "properties": {"maxAgeSeconds": {"type": "integer", "description": "The value, in seconds, to return in the  Access-Control-Max-Age header used in preflight responses.", "format": "int32"}, "method": {"type": "array", "description": "The list of HTTP methods on which to include CORS response headers, (GET, OPTIONS, POST, etc) Note: \"*\" is permitted in the list of methods, and means \"any method\".", "items": {"type": "string"}}, "origin": {"type": "array", "description": "The list of Origins eligible to receive CORS response headers. Note: \"*\" is permitted in the list of origins, and means \"any Origin\".", "items": {"type": "string"}}, "responseHeader": {"type": "array", "description": "The list of HTTP headers other than the simple response headers to give permission for the user-agent to share across domains.", "items": {"type": "string"}}}}}, "customPlacementConfig": {"type": "object", "description": "The bucket's custom placement configuration for Custom Dual Regions.", "properties": {"dataLocations": {"type": "array", "description": "The list of regional locations in which data is placed.", "items": {"type": "string"}}}}, "defaultEventBasedHold": {"type": "boolean", "description": "The default value for event-based hold on newly created objects in this bucket. Event-based hold is a way to retain objects indefinitely until an event occurs, signified by the hold's release. After being released, such objects will be subject to bucket-level retention (if any). One sample use case of this flag is for banks to hold loan documents for at least 3 years after loan is paid in full. Here, bucket-level retention is 3 years and the event is loan being paid in full. In this example, these objects will be held intact for any number of years until the event has occurred (event-based hold on the object is released) and then 3 more years after that. That means retention duration of the objects begins from the moment event-based hold transitioned from true to false. Objects under event-based hold cannot be deleted, overwritten or archived until the hold is removed."}, "defaultObjectAcl": {"type": "array", "description": "Default access controls to apply to new objects when no ACL is provided.", "items": {"$ref": "ObjectAccessControl"}}, "encryption": {"type": "object", "description": "Encryption configuration for a bucket.", "properties": {"defaultKmsKeyName": {"type": "string", "description": "A Cloud KMS key that will be used to encrypt objects inserted into this bucket, if no encryption method is specified."}}}, "etag": {"type": "string", "description": "HTTP 1.1 Entity tag for the bucket."}, "hierarchicalNamespace": {"type": "object", "description": "The bucket's hierarchical namespace configuration.", "properties": {"enabled": {"type": "boolean", "description": "When set to true, hierarchical namespace is enabled for this bucket."}}}, "iamConfiguration": {"type": "object", "description": "The bucket's IAM configuration.", "properties": {"bucketPolicyOnly": {"type": "object", "description": "The bucket's uniform bucket-level access configuration. The feature was formerly known as Bucket Policy Only. For backward compatibility, this field will be populated with identical information as the uniformBucketLevelAccess field. We recommend using the uniformBucketLevelAccess field to enable and disable the feature.", "properties": {"enabled": {"type": "boolean", "description": "If set, access is controlled only by bucket-level or above IAM policies."}, "lockedTime": {"type": "string", "description": "The deadline for changing iamConfiguration.bucketPolicyOnly.enabled from true to false in RFC 3339 format. iamConfiguration.bucketPolicyOnly.enabled may be changed from true to false until the locked time, after which the field is immutable.", "format": "date-time"}}}, "uniformBucketLevelAccess": {"type": "object", "description": "The bucket's uniform bucket-level access configuration.", "properties": {"enabled": {"type": "boolean", "description": "If set, access is controlled only by bucket-level or above IAM policies."}, "lockedTime": {"type": "string", "description": "The deadline for changing iamConfiguration.uniformBucketLevelAccess.enabled from true to false in RFC 3339  format. iamConfiguration.uniformBucketLevelAccess.enabled may be changed from true to false until the locked time, after which the field is immutable.", "format": "date-time"}}}, "publicAccessPrevention": {"type": "string", "description": "The bucket's Public Access Prevention configuration. Currently, 'inherited' and 'enforced' are supported."}}}, "id": {"type": "string", "description": "The ID of the bucket. For buckets, the id and name properties are the same."}, "kind": {"type": "string", "description": "The kind of item this is. For buckets, this is always storage#bucket.", "default": "storage#bucket"}, "labels": {"type": "object", "description": "User-provided labels, in key/value pairs.", "additionalProperties": {"type": "string", "description": "An individual label entry."}}, "lifecycle": {"type": "object", "description": "The bucket's lifecycle configuration. See lifecycle management for more information.", "properties": {"rule": {"type": "array", "description": "A lifecycle management rule, which is made of an action to take and the condition(s) under which the action will be taken.", "items": {"type": "object", "properties": {"action": {"type": "object", "description": "The action to take.", "properties": {"storageClass": {"type": "string", "description": "Target storage class. Required iff the type of the action is SetStorageClass."}, "type": {"type": "string", "description": "Type of the action. Currently, only Delete, SetStorageClass, and AbortIncompleteMultipartUpload are supported."}}}, "condition": {"type": "object", "description": "The condition(s) under which the action will be taken.", "properties": {"age": {"type": "integer", "description": "Age of an object (in days). This condition is satisfied when an object reaches the specified age.", "format": "int32"}, "createdBefore": {"type": "string", "description": "A date in RFC 3339 format with only the date part (for instance, \"2013-01-15\"). This condition is satisfied when an object is created before midnight of the specified date in UTC.", "format": "date"}, "customTimeBefore": {"type": "string", "description": "A date in RFC 3339 format with only the date part (for instance, \"2013-01-15\"). This condition is satisfied when the custom time on an object is before this date in UTC.", "format": "date"}, "daysSinceCustomTime": {"type": "integer", "description": "Number of days elapsed since the user-specified timestamp set on an object. The condition is satisfied if the days elapsed is at least this number. If no custom timestamp is specified on an object, the condition does not apply.", "format": "int32"}, "daysSinceNoncurrentTime": {"type": "integer", "description": "Number of days elapsed since the noncurrent timestamp of an object. The condition is satisfied if the days elapsed is at least this number. This condition is relevant only for versioned objects. The value of the field must be a nonnegative integer. If it's zero, the object version will become eligible for Lifecycle action as soon as it becomes noncurrent.", "format": "int32"}, "isLive": {"type": "boolean", "description": "Relevant only for versioned objects. If the value is true, this condition matches live objects; if the value is false, it matches archived objects."}, "matchesPattern": {"type": "string", "description": "A regular expression that satisfies the RE2 syntax. This condition is satisfied when the name of the object matches the RE2 pattern. Note: This feature is currently in the \"Early Access\" launch stage and is only available to a whitelisted set of users; that means that this feature may be changed in backward-incompatible ways and that it is not guaranteed to be released."}, "matchesPrefix": {"type": "array", "description": "List of object name prefixes. This condition will be satisfied when at least one of the prefixes exactly matches the beginning of the object name.", "items": {"type": "string"}}, "matchesSuffix": {"type": "array", "description": "List of object name suffixes. This condition will be satisfied when at least one of the suffixes exactly matches the end of the object name.", "items": {"type": "string"}}, "matchesStorageClass": {"type": "array", "description": "Objects having any of the storage classes specified by this condition will be matched. Values include MULTI_REGIONAL, REGIONAL, NEARLIN<PERSON>, COLDLINE, ARCHIVE, STANDARD, and DURABLE_REDUCED_AVAILABILITY.", "items": {"type": "string"}}, "noncurrentTimeBefore": {"type": "string", "description": "A date in RFC 3339 format with only the date part (for instance, \"2013-01-15\"). This condition is satisfied when the noncurrent time on an object is before this date in UTC. This condition is relevant only for versioned objects.", "format": "date"}, "numNewerVersions": {"type": "integer", "description": "Relevant only for versioned objects. If the value is N, this condition is satisfied when there are at least N versions (including the live version) newer than this version of the object.", "format": "int32"}}}}}}}}, "autoclass": {"type": "object", "description": "The bucket's Autoclass configuration.", "properties": {"enabled": {"type": "boolean", "description": "Whether or not Autoclass is enabled on this bucket"}, "toggleTime": {"type": "string", "description": "A date and time in RFC 3339 format representing the instant at which \"enabled\" was last toggled.", "format": "date-time"}, "terminalStorageClass": {"type": "string", "description": "The storage class that objects in the bucket eventually transition to if they are not read for a certain length of time. Valid values are NEARLINE and ARCHIVE."}, "terminalStorageClassUpdateTime": {"type": "string", "description": "A date and time in RFC 3339 format representing the time of the most recent update to \"terminalStorageClass\".", "format": "date-time"}}}, "location": {"type": "string", "description": "The location of the bucket. Object data for objects in the bucket resides in physical storage within this region. Defaults to US. See the developer's guide for the authoritative list."}, "locationType": {"type": "string", "description": "The type of the bucket location."}, "logging": {"type": "object", "description": "The bucket's logging configuration, which defines the destination bucket and optional name prefix for the current bucket's logs.", "properties": {"logBucket": {"type": "string", "description": "The destination bucket where the current bucket's logs should be placed."}, "logObjectPrefix": {"type": "string", "description": "A prefix for log object names."}}}, "metageneration": {"type": "string", "description": "The metadata generation of this bucket.", "format": "int64"}, "name": {"type": "string", "description": "The name of the bucket.", "annotations": {"required": ["storage.buckets.insert"]}}, "generation": {"type": "string", "description": "The version of the bucket.", "format": "int64"}, "owner": {"type": "object", "description": "The owner of the bucket. This is always the project team's owner group.", "properties": {"entity": {"type": "string", "description": "The entity, in the form project-owner-projectId."}, "entityId": {"type": "string", "description": "The ID for the entity."}}}, "projectNumber": {"type": "string", "description": "The project number of the project the bucket belongs to.", "format": "uint64"}, "retentionPolicy": {"type": "object", "description": "The bucket's retention policy. The retention policy enforces a minimum retention time for all objects contained in the bucket, based on their creation time. Any attempt to overwrite or delete objects younger than the retention period will result in a PERMISSION_DENIED error. An unlocked retention policy can be modified or removed from the bucket via a storage.buckets.update operation. A locked retention policy cannot be removed or shortened in duration for the lifetime of the bucket. Attempting to remove or decrease period of a locked retention policy will result in a PERMISSION_DENIED error.", "properties": {"effectiveTime": {"type": "string", "description": "Server-determined value that indicates the time from which policy was enforced and effective. This value is in RFC 3339 format.", "format": "date-time"}, "isLocked": {"type": "boolean", "description": "Once locked, an object retention policy cannot be modified."}, "retentionPeriod": {"type": "string", "description": "The duration in seconds that objects need to be retained. Retention duration must be greater than zero and less than 100 years. Note that enforcement of retention periods less than a day is not guaranteed. Such periods should only be used for testing purposes.", "format": "int64"}}}, "objectRetention": {"type": "object", "description": "The bucket's object retention config.", "properties": {"mode": {"type": "string", "description": "The bucket's object retention mode. Can be Enabled."}}}, "rpo": {"type": "string", "description": "The Recovery Point Objective (RPO) of this bucket. Set to ASYNC_TURBO to turn on Turbo Replication on a bucket."}, "selfLink": {"type": "string", "description": "The URI of this bucket."}, "softDeletePolicy": {"type": "object", "description": "The bucket's soft delete policy, which defines the period of time that soft-deleted objects will be retained, and cannot be permanently deleted.", "properties": {"retentionDurationSeconds": {"type": "string", "description": "The duration in seconds that soft-deleted objects in the bucket will be retained and cannot be permanently deleted.", "format": "int64"}, "effectiveTime": {"type": "string", "description": "Server-determined value that indicates the time from which the policy, or one with a greater retention, was effective. This value is in RFC 3339 format.", "format": "date-time"}}}, "softDeleteTime": {"type": "string", "description": "The time at which the bucket was soft-deleted.", "format": "date-time"}, "hardDeleteTime": {"type": "string", "description": "The time when a soft-deleted bucket is permanently deleted and can no longer be restored.", "format": "date-time"}, "storageClass": {"type": "string", "description": "The bucket's default storage class, used whenever no storageClass is specified for a newly-created object. This defines how objects in the bucket are stored and determines the SLA and the cost of storage. Values include MULTI_REGIONAL, REGIONAL, STANDARD, NEARLINE, COLDLINE, ARCHIVE, and DURABLE_REDUCED_AVAILABILITY. If this value is not specified when the bucket is created, it will default to STANDARD. For more information, see storage classes."}, "timeCreated": {"type": "string", "description": "The creation time of the bucket in RFC 3339 format.", "format": "date-time"}, "updated": {"type": "string", "description": "The modification time of the bucket in RFC 3339 format.", "format": "date-time"}, "versioning": {"type": "object", "description": "The bucket's versioning configuration.", "properties": {"enabled": {"type": "boolean", "description": "While set to true, versioning is fully enabled for this bucket."}}}, "website": {"type": "object", "description": "The bucket's website configuration, controlling how the service behaves when accessing bucket contents as a web site. See the Static Website Examples for more information.", "properties": {"mainPageSuffix": {"type": "string", "description": "If the requested object path is missing, the service will ensure the path has a trailing '/', append this suffix, and attempt to retrieve the resulting object. This allows the creation of index.html objects to represent directory pages."}, "notFoundPage": {"type": "string", "description": "If the requested object path is missing, and any mainPageSuffix object is missing, if applicable, the service will return the named object from this bucket as the content for a 404 Not Found result."}}}, "satisfiesPZS": {"type": "boolean", "description": "Reserved for future use."}}}, "AnywhereCache": {"id": "AnywhereCache", "type": "object", "description": "An Anywhere Cache instance.", "properties": {"kind": {"type": "string", "description": "The kind of item this is. For Anywhere Cache, this is always storage#anywhereCache.", "default": "storage#anywhereCache"}, "id": {"type": "string", "description": "The ID of the resource, including the project number, bucket name and anywhere cache ID."}, "selfLink": {"type": "string", "description": "The link to this cache instance."}, "bucket": {"type": "string", "description": "The name of the bucket containing this cache instance."}, "anywhereCacheId": {"type": "string", "description": "The ID of the Anywhere cache instance."}, "zone": {"type": "string", "description": "The zone in which the cache instance is running. For example, us-central1-a."}, "state": {"type": "string", "description": "The current state of the cache instance."}, "createTime": {"type": "string", "description": "The creation time of the cache instance in RFC 3339 format.", "format": "date-time"}, "updateTime": {"type": "string", "description": "The modification time of the cache instance metadata in RFC 3339 format.", "format": "date-time"}, "ttl": {"type": "string", "description": "The TTL of all cache entries in whole seconds. e.g., \"7200s\". ", "format": "google-duration"}, "admissionPolicy": {"type": "string", "description": "The cache-level entry admission policy."}, "pendingUpdate": {"type": "boolean", "description": "True if the cache instance has an active Update long-running operation."}}}, "AnywhereCaches": {"id": "AnywhereCaches", "type": "object", "description": "A list of Anywhere Caches.", "properties": {"kind": {"type": "string", "description": "The kind of item this is. For lists of Anywhere Caches, this is always storage#anywhereCaches.", "default": "storage#anywhereCaches"}, "nextPageToken": {"type": "string", "description": "The continuation token, used to page through large result sets. Provide this value in a subsequent request to return the next page of results."}, "items": {"type": "array", "description": "The list of items.", "items": {"$ref": "AnywhereCache"}}}}, "BucketAccessControl": {"id": "BucketAccessControl", "type": "object", "description": "An access-control entry.", "properties": {"bucket": {"type": "string", "description": "The name of the bucket."}, "domain": {"type": "string", "description": "The domain associated with the entity, if any."}, "email": {"type": "string", "description": "The email address associated with the entity, if any."}, "entity": {"type": "string", "description": "The entity holding the permission, in one of the following forms: \n- user-userId \n- user-email \n- group-groupId \n- group-email \n- domain-domain \n- project-team-projectId \n- allUsers \n- allAuthenticatedUsers Examples: \n- <NAME_EMAIL> <NAME_EMAIL>. \n- <NAME_EMAIL> <NAME_EMAIL>. \n- To refer to all members of the Google Apps for Business domain example.com, the entity would be domain-example.com.", "annotations": {"required": ["storage.bucketAccessControls.insert"]}}, "entityId": {"type": "string", "description": "The ID for the entity, if any."}, "etag": {"type": "string", "description": "HTTP 1.1 Entity tag for the access-control entry."}, "id": {"type": "string", "description": "The ID of the access-control entry."}, "kind": {"type": "string", "description": "The kind of item this is. For bucket access control entries, this is always storage#bucketAccessControl.", "default": "storage#bucketAccessControl"}, "projectTeam": {"type": "object", "description": "The project team associated with the entity, if any.", "properties": {"projectNumber": {"type": "string", "description": "The project number."}, "team": {"type": "string", "description": "The team."}}}, "role": {"type": "string", "description": "The access permission for the entity.", "annotations": {"required": ["storage.bucketAccessControls.insert"]}}, "selfLink": {"type": "string", "description": "The link to this access-control entry."}}}, "BucketAccessControls": {"id": "BucketAccessControls", "type": "object", "description": "An access-control list.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "BucketAccessControl"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of bucket access control entries, this is always storage#bucketAccessControls.", "default": "storage#bucketAccessControls"}}}, "Buckets": {"id": "Buckets", "type": "object", "description": "A list of buckets.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "Bucket"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of buckets, this is always storage#buckets.", "default": "storage#buckets"}, "nextPageToken": {"type": "string", "description": "The continuation token, used to page through large result sets. Provide this value in a subsequent request to return the next page of results."}}}, "Channel": {"id": "Channel", "type": "object", "description": "An notification channel used to watch for resource changes.", "properties": {"address": {"type": "string", "description": "The address where notifications are delivered for this channel."}, "expiration": {"type": "string", "description": "Date and time of notification channel expiration, expressed as a Unix timestamp, in milliseconds. Optional.", "format": "int64"}, "id": {"type": "string", "description": "A UUID or similar unique string that identifies this channel."}, "kind": {"type": "string", "description": "Identifies this as a notification channel used to watch for changes to a resource, which is \"api#channel\".", "default": "api#channel"}, "params": {"type": "object", "description": "Additional parameters controlling delivery channel behavior. Optional.", "additionalProperties": {"type": "string", "description": "Declares a new parameter by name."}}, "payload": {"type": "boolean", "description": "A Boolean value to indicate whether payload is wanted. Optional."}, "resourceId": {"type": "string", "description": "An opaque ID that identifies the resource being watched on this channel. Stable across different API versions."}, "resourceUri": {"type": "string", "description": "A version-specific identifier for the watched resource."}, "token": {"type": "string", "description": "An arbitrary string delivered to the target address with each notification delivered over this channel. Optional."}, "type": {"type": "string", "description": "The type of delivery mechanism used for this channel."}}}, "ComposeRequest": {"id": "ComposeRequest", "type": "object", "description": "A Compose request.", "properties": {"destination": {"$ref": "Object", "description": "Properties of the resulting object."}, "kind": {"type": "string", "description": "The kind of item this is.", "default": "storage#composeRequest"}, "sourceObjects": {"type": "array", "description": "The list of source objects that will be concatenated into a single object.", "items": {"type": "object", "properties": {"generation": {"type": "string", "description": "The generation of this object to use as the source.", "format": "int64"}, "name": {"type": "string", "description": "The source object's name. All source objects must reside in the same bucket.", "annotations": {"required": ["storage.objects.compose"]}}, "objectPreconditions": {"type": "object", "description": "Conditions that must be met for this operation to execute.", "properties": {"ifGenerationMatch": {"type": "string", "description": "Only perform the composition if the generation of the source object that would be used matches this value. If this value and a generation are both specified, they must be the same value or the call will fail.", "format": "int64"}}}}}, "annotations": {"required": ["storage.objects.compose"]}}}}, "Folder": {"id": "Folder", "type": "object", "description": "A folder. Only available in buckets with hierarchical namespace enabled.", "properties": {"bucket": {"type": "string", "description": "The name of the bucket containing this folder."}, "id": {"type": "string", "description": "The ID of the folder, including the bucket name, folder name."}, "kind": {"type": "string", "description": "The kind of item this is. For folders, this is always storage#folder.", "default": "storage#folder"}, "metageneration": {"type": "string", "description": "The version of the metadata for this folder. Used for preconditions and for detecting changes in metadata.", "format": "int64"}, "name": {"type": "string", "description": "The name of the folder. Required if not specified by URL parameter."}, "selfLink": {"type": "string", "description": "The link to this folder."}, "createTime": {"type": "string", "description": "The creation time of the folder in RFC 3339 format.", "format": "date-time"}, "updateTime": {"type": "string", "description": "The modification time of the folder metadata in RFC 3339 format.", "format": "date-time"}, "pendingRenameInfo": {"type": "object", "description": "Only present if the folder is part of an ongoing rename folder operation. Contains information which can be used to query the operation status.", "properties": {"operationId": {"type": "string", "description": "The ID of the rename folder operation."}}}}}, "Folders": {"id": "Folders", "type": "object", "description": "A list of folders.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "Folder"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of folders, this is always storage#folders.", "default": "storage#folders"}, "nextPageToken": {"type": "string", "description": "The continuation token, used to page through large result sets. Provide this value in a subsequent request to return the next page of results."}}}, "Expr": {"id": "Expr", "type": "object", "description": "Represents an expression text. Example: title: \"User account presence\" description: \"Determines whether the request has a user account\" expression: \"size(request.user) > 0\"", "properties": {"description": {"type": "string", "description": "An optional description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI."}, "expression": {"type": "string", "description": "Textual representation of an expression in Common Expression Language syntax. The application context of the containing message determines which well-known feature set of CEL is supported."}, "location": {"type": "string", "description": "An optional string indicating the location of the expression for error reporting, e.g. a file name and a position in the file."}, "title": {"type": "string", "description": "An optional title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression."}}}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is \"false\", it means the operation is still in progress. If \"true\", the operation is completed, and either \"error\" or \"response\" is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the \"name\" should be a resource name ending with \"operations/{operationId}\".", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal response of the operation in case of success. If the original method returns no data on success, such as \"Delete\", the response is google.protobuf.Empty. If the original method is standard Get/Create/Update, the response should be the resource. For other methods, the response should have the type \"XxxResponse\", where \"Xxx\" is the original method name. For example, if the original method name is \"TakeSnapshot()\", the inferred response type is \"TakeSnapshotResponse\".", "type": "object"}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for storage.buckets.operations.list.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"type": "string", "description": "The continuation token, used to page through large result sets. Provide this value in a subsequent request to return the next page of results."}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleRpcStatus": {"description": "The \"Status\" type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each \"Status\" message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English.", "type": "string"}}, "type": "object"}, "HmacKey": {"id": "Hmac<PERSON>ey", "type": "object", "description": "JSON template to produce a JSON-style HMAC Key resource for Create responses.", "properties": {"kind": {"type": "string", "description": "The kind of item this is. For HMAC keys, this is always storage#hmacKey.", "default": "storage#hmacKey"}, "metadata": {"$ref": "HmacKeyMetadata", "description": "Key metadata."}, "secret": {"type": "string", "description": "HMAC secret key material."}}}, "HmacKeyMetadata": {"id": "HmacKeyMetadata", "type": "object", "description": "JSON template to produce a JSON-style HMAC Key metadata resource.", "properties": {"accessId": {"type": "string", "description": "The ID of the HMAC Key."}, "etag": {"type": "string", "description": "HTTP 1.1 Entity tag for the HMAC key."}, "id": {"type": "string", "description": "The ID of the HMAC key, including the Project ID and the Access ID."}, "kind": {"type": "string", "description": "The kind of item this is. For HMAC Key metadata, this is always storage#hmacKeyMetadata.", "default": "storage#hmacKeyMetadata"}, "projectId": {"type": "string", "description": "Project ID owning the service account to which the key authenticates."}, "selfLink": {"type": "string", "description": "The link to this resource."}, "serviceAccountEmail": {"type": "string", "description": "The email address of the key's associated service account."}, "state": {"type": "string", "description": "The state of the key. Can be one of ACTIVE, INACTIVE, or DELETED."}, "timeCreated": {"type": "string", "description": "The creation time of the HMAC key in RFC 3339 format.", "format": "date-time"}, "updated": {"type": "string", "description": "The last modification time of the HMAC key metadata in RFC 3339 format.", "format": "date-time"}}}, "HmacKeysMetadata": {"id": "HmacKeysMetadata", "type": "object", "description": "A list of hmacKeys.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "HmacKeyMetadata"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of hmacKeys, this is always storage#hmacKeysMetadata.", "default": "storage#hmacKeysMetadata"}, "nextPageToken": {"type": "string", "description": "The continuation token, used to page through large result sets. Provide this value in a subsequent request to return the next page of results."}}}, "ManagedFolder": {"id": "ManagedFolder", "type": "object", "description": "A managed folder.", "properties": {"bucket": {"type": "string", "description": "The name of the bucket containing this managed folder."}, "id": {"type": "string", "description": "The ID of the managed folder, including the bucket name and managed folder name."}, "kind": {"type": "string", "description": "The kind of item this is. For managed folders, this is always storage#managedFolder.", "default": "storage#managedFolder"}, "metageneration": {"type": "string", "description": "The version of the metadata for this managed folder. Used for preconditions and for detecting changes in metadata.", "format": "int64"}, "name": {"type": "string", "description": "The name of the managed folder. Required if not specified by URL parameter."}, "selfLink": {"type": "string", "description": "The link to this managed folder."}, "createTime": {"type": "string", "description": "The creation time of the managed folder in RFC 3339 format.", "format": "date-time"}, "updateTime": {"type": "string", "description": "The last update time of the managed folder metadata in RFC 3339 format.", "format": "date-time"}}}, "ManagedFolders": {"id": "ManagedFolders", "type": "object", "description": "A list of managed folders.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "ManagedFolder"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of managed folders, this is always storage#managedFolders.", "default": "storage#managedFolders"}, "nextPageToken": {"type": "string", "description": "The continuation token, used to page through large result sets. Provide this value in a subsequent request to return the next page of results."}}}, "Notification": {"id": "Notification", "type": "object", "description": "A subscription to receive Google PubSub notifications.", "properties": {"custom_attributes": {"type": "object", "description": "An optional list of additional attributes to attach to each Cloud PubSub message published for this notification subscription.", "additionalProperties": {"type": "string"}}, "etag": {"type": "string", "description": "HTTP 1.1 Entity tag for this subscription notification."}, "event_types": {"type": "array", "description": "If present, only send notifications about listed event types. If empty, sent notifications for all event types.", "items": {"type": "string"}}, "id": {"type": "string", "description": "The ID of the notification."}, "kind": {"type": "string", "description": "The kind of item this is. For notifications, this is always storage#notification.", "default": "storage#notification"}, "object_name_prefix": {"type": "string", "description": "If present, only apply this notification configuration to object names that begin with this prefix."}, "payload_format": {"type": "string", "description": "The desired content of the Payload.", "default": "JSON_API_V1", "annotations": {"required": ["storage.notifications.insert"]}}, "selfLink": {"type": "string", "description": "The canonical URL of this notification."}, "topic": {"type": "string", "description": "The Cloud PubSub topic to which this subscription publishes. Formatted as: '//pubsub.googleapis.com/projects/{project-identifier}/topics/{my-topic}'", "annotations": {"required": ["storage.notifications.insert"]}}}}, "Notifications": {"id": "Notifications", "type": "object", "description": "A list of notification subscriptions.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "Notification"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of notifications, this is always storage#notifications.", "default": "storage#notifications"}}}, "Object": {"id": "Object", "type": "object", "description": "An object.", "properties": {"acl": {"type": "array", "description": "Access controls on the object.", "items": {"$ref": "ObjectAccessControl"}, "annotations": {"required": ["storage.objects.update"]}}, "bucket": {"type": "string", "description": "The name of the bucket containing this object."}, "cacheControl": {"type": "string", "description": "Cache-Control directive for the object data. If omitted, and the object is accessible to all anonymous users, the default will be public, max-age=3600."}, "componentCount": {"type": "integer", "description": "Number of underlying components that make up this object. Components are accumulated by compose operations.", "format": "int32"}, "contentDisposition": {"type": "string", "description": "Content-Disposition of the object data."}, "contentEncoding": {"type": "string", "description": "Content-Encoding of the object data."}, "contentLanguage": {"type": "string", "description": "Content-Language of the object data."}, "contentType": {"type": "string", "description": "Content-Type of the object data. If an object is stored without a Content-Type, it is served as application/octet-stream."}, "crc32c": {"type": "string", "description": "CRC32c checksum, as described in RFC 4960, Appendix B; encoded using base64 in big-endian byte order. For more information about using the CRC32c checksum, see Hashes and ETags: Best Practices."}, "customTime": {"type": "string", "description": "A timestamp in RFC 3339 format specified by the user for an object.", "format": "date-time"}, "customerEncryption": {"type": "object", "description": "Metadata of customer-supplied encryption key, if the object is encrypted by such a key.", "properties": {"encryptionAlgorithm": {"type": "string", "description": "The encryption algorithm."}, "keySha256": {"type": "string", "description": "SHA256 hash value of the encryption key."}}}, "etag": {"type": "string", "description": "HTTP 1.1 Entity tag for the object."}, "eventBasedHold": {"type": "boolean", "description": "Whether an object is under event-based hold. Event-based hold is a way to retain objects until an event occurs, which is signified by the hold's release (i.e. this value is set to false). After being released (set to false), such objects will be subject to bucket-level retention (if any). One sample use case of this flag is for banks to hold loan documents for at least 3 years after loan is paid in full. Here, bucket-level retention is 3 years and the event is the loan being paid in full. In this example, these objects will be held intact for any number of years until the event has occurred (event-based hold on the object is released) and then 3 more years after that. That means retention duration of the objects begins from the moment event-based hold transitioned from true to false."}, "generation": {"type": "string", "description": "The content generation of this object. Used for object versioning.", "format": "int64"}, "id": {"type": "string", "description": "The ID of the object, including the bucket name, object name, and generation number."}, "kind": {"type": "string", "description": "The kind of item this is. For objects, this is always storage#object.", "default": "storage#object"}, "kmsKeyName": {"type": "string", "description": "Not currently supported. Specifying the parameter causes the request to fail with status code 400 - Bad Request."}, "md5Hash": {"type": "string", "description": "MD5 hash of the data; encoded using base64. For more information about using the MD5 hash, see Hashes and ETags: Best Practices."}, "mediaLink": {"type": "string", "description": "Media download link."}, "metadata": {"type": "object", "description": "User-provided metadata, in key/value pairs.", "additionalProperties": {"type": "string", "description": "An individual metadata entry."}}, "metageneration": {"type": "string", "description": "The version of the metadata for this object at this generation. Used for preconditions and for detecting changes in metadata. A metageneration number is only meaningful in the context of a particular generation of a particular object.", "format": "int64"}, "name": {"type": "string", "description": "The name of the object. Required if not specified by URL parameter."}, "owner": {"type": "object", "description": "The owner of the object. This will always be the uploader of the object.", "properties": {"entity": {"type": "string", "description": "The entity, in the form user-userId."}, "entityId": {"type": "string", "description": "The ID for the entity."}}}, "retentionExpirationTime": {"type": "string", "description": "A server-determined value that specifies the earliest time that the object's retention period expires. This value is in RFC 3339 format. Note 1: This field is not provided for objects with an active event-based hold, since retention expiration is unknown until the hold is removed. Note 2: This value can be provided even when temporary hold is set (so that the user can reason about policy without having to first unset the temporary hold).", "format": "date-time"}, "retention": {"type": "object", "description": "A collection of object level retention parameters.", "properties": {"retainUntilTime": {"type": "string", "description": "A time in RFC 3339 format until which object retention protects this object.", "format": "date-time"}, "mode": {"type": "string", "description": "The bucket's object retention mode, can only be Unlocked or Locked."}}}, "selfLink": {"type": "string", "description": "The link to this object."}, "size": {"type": "string", "description": "Content-Length of the data in bytes.", "format": "uint64"}, "storageClass": {"type": "string", "description": "Storage class of the object."}, "temporaryHold": {"type": "boolean", "description": "Whether an object is under temporary hold. While this flag is set to true, the object is protected against deletion and overwrites. A common use case of this flag is regulatory investigations where objects need to be retained while the investigation is ongoing. Note that unlike event-based hold, temporary hold does not impact retention expiration time of an object."}, "timeCreated": {"type": "string", "description": "The creation time of the object in RFC 3339 format.", "format": "date-time"}, "timeDeleted": {"type": "string", "description": "The time at which the object became noncurrent in RFC 3339 format. Will be returned if and only if this version of the object has been deleted.", "format": "date-time"}, "softDeleteTime": {"type": "string", "description": "The time at which the object became soft-deleted in RFC 3339 format.", "format": "date-time"}, "hardDeleteTime": {"type": "string", "description": "This is the time (in the future) when the soft-deleted object will no longer be restorable. It is equal to the soft delete time plus the current soft delete retention duration of the bucket.", "format": "date-time"}, "timeStorageClassUpdated": {"type": "string", "description": "The time at which the object's storage class was last changed. When the object is initially created, it will be set to timeCreated.", "format": "date-time"}, "updated": {"type": "string", "description": "The modification time of the object metadata in RFC 3339 format. Set initially to object creation time and then updated whenever any metadata of the object changes. This includes changes made by a requester, such as modifying custom metadata, as well as changes made by Cloud Storage on behalf of a requester, such as changing the storage class based on an Object Lifecycle Configuration.", "format": "date-time"}}}, "ObjectAccessControl": {"id": "ObjectAccessControl", "type": "object", "description": "An access-control entry.", "properties": {"bucket": {"type": "string", "description": "The name of the bucket."}, "domain": {"type": "string", "description": "The domain associated with the entity, if any."}, "email": {"type": "string", "description": "The email address associated with the entity, if any."}, "entity": {"type": "string", "description": "The entity holding the permission, in one of the following forms: \n- user-userId \n- user-email \n- group-groupId \n- group-email \n- domain-domain \n- project-team-projectId \n- allUsers \n- allAuthenticatedUsers Examples: \n- <NAME_EMAIL> <NAME_EMAIL>. \n- <NAME_EMAIL> <NAME_EMAIL>. \n- To refer to all members of the Google Apps for Business domain example.com, the entity would be domain-example.com.", "annotations": {"required": ["storage.defaultObjectAccessControls.insert", "storage.objectAccessControls.insert"]}}, "entityId": {"type": "string", "description": "The ID for the entity, if any."}, "etag": {"type": "string", "description": "HTTP 1.1 Entity tag for the access-control entry."}, "generation": {"type": "string", "description": "The content generation of the object, if applied to an object.", "format": "int64"}, "id": {"type": "string", "description": "The ID of the access-control entry."}, "kind": {"type": "string", "description": "The kind of item this is. For object access control entries, this is always storage#objectAccessControl.", "default": "storage#objectAccessControl"}, "object": {"type": "string", "description": "The name of the object, if applied to an object."}, "projectTeam": {"type": "object", "description": "The project team associated with the entity, if any.", "properties": {"projectNumber": {"type": "string", "description": "The project number."}, "team": {"type": "string", "description": "The team."}}}, "role": {"type": "string", "description": "The access permission for the entity.", "annotations": {"required": ["storage.defaultObjectAccessControls.insert", "storage.objectAccessControls.insert"]}}, "selfLink": {"type": "string", "description": "The link to this access-control entry."}}}, "ObjectAccessControls": {"id": "ObjectAccessControls", "type": "object", "description": "An access-control list.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "ObjectAccessControl"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of object access control entries, this is always storage#objectAccessControls.", "default": "storage#objectAccessControls"}}}, "Objects": {"id": "Objects", "type": "object", "description": "A list of objects.", "properties": {"items": {"type": "array", "description": "The list of items.", "items": {"$ref": "Object"}}, "kind": {"type": "string", "description": "The kind of item this is. For lists of objects, this is always storage#objects.", "default": "storage#objects"}, "nextPageToken": {"type": "string", "description": "The continuation token, used to page through large result sets. Provide this value in a subsequent request to return the next page of results."}, "prefixes": {"type": "array", "description": "The list of prefixes of objects matching-but-not-listed up to and including the requested delimiter.", "items": {"type": "string"}}}}, "Policy": {"id": "Policy", "type": "object", "description": "A bucket/object/managedFolder IAM policy.", "properties": {"bindings": {"type": "array", "description": "An association between a role, which comes with a set of permissions, and members who may assume that role.", "items": {"type": "object", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. NOTE: an unsatisfied condition will not allow user access via current binding. Different bindings, including their conditions, are examined independently."}, "members": {"type": "array", "description": "A collection of identifiers for members who may assume the provided role. Recognized identifiers are as follows:  \n- allUsers — A special identifier that represents anyone on the internet; with or without a Google account.  \n- allAuthenticatedUsers — A special identifier that represents anyone who is authenticated with a Google account or a service account.  \n- user:emailid — An email address that represents a specific account. For example, user:<EMAIL> or user:<EMAIL>.  \n- serviceAccount:emailid — An email address that represents a service account. For example,  serviceAccount:<EMAIL> .  \n- group:emailid — An email address that represents a Google group. For example, group:<EMAIL>.  \n- domain:domain — A Google Apps domain name that represents all the users of that domain. For example, domain:google.com or domain:example.com.  \n- projectOwner:projectid — Owners of the given project. For example, projectOwner:my-example-project  \n- projectEditor:projectid — Editors of the given project. For example, projectEditor:my-example-project  \n- projectViewer:projectid — Viewers of the given project. For example, projectViewer:my-example-project", "items": {"type": "string"}, "annotations": {"required": ["storage.buckets.setIamPolicy", "storage.objects.setIamPolicy", "storage.managedFolders.setIamPolicy"]}}, "role": {"type": "string", "description": "The role to which members belong. Two types of roles are supported: new IAM roles, which grant permissions that do not map directly to those provided by ACLs, and legacy IAM roles, which do map directly to ACL permissions. All roles are of the format roles/storage.specificRole.\nThe new IAM roles are:  \n- roles/storage.admin — Full control of Google Cloud Storage resources.  \n- roles/storage.objectViewer — Read-Only access to Google Cloud Storage objects.  \n- roles/storage.objectCreator — Access to create objects in Google Cloud Storage.  \n- roles/storage.objectAdmin — Full control of Google Cloud Storage objects.   The legacy IAM roles are:  \n- roles/storage.legacyObjectReader — Read-only access to objects without listing. Equivalent to an ACL entry on an object with the READER role.  \n- roles/storage.legacyObjectOwner — Read/write access to existing objects without listing. Equivalent to an ACL entry on an object with the OWNER role.  \n- roles/storage.legacyBucketReader — Read access to buckets with object listing. Equivalent to an ACL entry on a bucket with the READER role.  \n- roles/storage.legacyBucketWriter — Read access to buckets with object listing/creation/deletion. Equivalent to an ACL entry on a bucket with the WRITER role.  \n- roles/storage.legacyBucketOwner — Read and write access to existing buckets with object listing/creation/deletion. Equivalent to an ACL entry on a bucket with the OWNER role.", "annotations": {"required": ["storage.buckets.setIamPolicy", "storage.objects.setIamPolicy", "storage.managedFolders.setIamPolicy"]}}}}, "annotations": {"required": ["storage.buckets.setIamPolicy", "storage.objects.setIamPolicy", "storage.managedFolders.setIamPolicy"]}}, "etag": {"type": "string", "description": "HTTP 1.1  Entity tag for the policy.", "format": "byte"}, "kind": {"type": "string", "description": "The kind of item this is. For policies, this is always storage#policy. This field is ignored on input.", "default": "storage#policy"}, "resourceId": {"type": "string", "description": "The ID of the resource to which this policy belongs. Will be of the form projects/_/buckets/bucket for buckets, projects/_/buckets/bucket/objects/object for objects, and projects/_/buckets/bucket/managedFolders/managedFolder. A specific generation may be specified by appending #generationNumber to the end of the object name, e.g. projects/_/buckets/my-bucket/objects/data.txt#17. The current generation can be denoted with #0. This field is ignored on input."}, "version": {"type": "integer", "description": "The IAM policy format version.", "format": "int32"}}}, "RewriteResponse": {"id": "RewriteResponse", "type": "object", "description": "A rewrite response.", "properties": {"done": {"type": "boolean", "description": "true if the copy is finished; otherwise, false if the copy is in progress. This property is always present in the response."}, "kind": {"type": "string", "description": "The kind of item this is.", "default": "storage#rewriteResponse"}, "objectSize": {"type": "string", "description": "The total size of the object being copied in bytes. This property is always present in the response.", "format": "int64"}, "resource": {"$ref": "Object", "description": "A resource containing the metadata for the copied-to object. This property is present in the response only when copying completes."}, "rewriteToken": {"type": "string", "description": "A token to use in subsequent requests to continue copying data. This token is present in the response only when there is more data to copy."}, "totalBytesRewritten": {"type": "string", "description": "The total bytes written so far, which can be used to provide a waiting user with a progress indicator. This property is always present in the response.", "format": "int64"}}}, "ServiceAccount": {"id": "ServiceAccount", "type": "object", "description": "A subscription to receive Google PubSub notifications.", "properties": {"email_address": {"type": "string", "description": "The ID of the notification."}, "kind": {"type": "string", "description": "The kind of item this is. For notifications, this is always storage#notification.", "default": "storage#serviceAccount"}}}, "TestIamPermissionsResponse": {"id": "TestIamPermissionsResponse", "type": "object", "description": "A storage.(buckets|objects|managedFolders).testIamPermissions response.", "properties": {"kind": {"type": "string", "description": "The kind of item this is.", "default": "storage#testIamPermissionsResponse"}, "permissions": {"type": "array", "description": "The permissions held by the caller. Permissions are always of the format storage.resource.capability, where resource is one of buckets, objects, or managedFolders. The supported permissions are as follows:  \n- storage.buckets.delete — Delete bucket.  \n- storage.buckets.get — Read bucket metadata.  \n- storage.buckets.getIamPolicy — Read bucket IAM policy.  \n- storage.buckets.create — Create bucket.  \n- storage.buckets.list — List buckets.  \n- storage.buckets.setIamPolicy — Update bucket IAM policy.  \n- storage.buckets.update — Update bucket metadata.  \n- storage.objects.delete — Delete object.  \n- storage.objects.get — Read object data and metadata.  \n- storage.objects.getIamPolicy — Read object IAM policy.  \n- storage.objects.create — Create object.  \n- storage.objects.list — List objects.  \n- storage.objects.setIamPolicy — Update object IAM policy.  \n- storage.objects.update — Update object metadata. \n- storage.managedFolders.delete — Delete managed folder.  \n- storage.managedFolders.get — Read managed folder metadata.  \n- storage.managedFolders.getIamPolicy — Read managed folder IAM policy.  \n- storage.managedFolders.create — Create managed folder.  \n- storage.managedFolders.list — List managed folders.  \n- storage.managedFolders.setIamPolicy — Update managed folder IAM policy.", "items": {"type": "string"}}}}, "BulkRestoreObjectsRequest": {"id": "BulkRestoreObjectsRequest", "type": "object", "description": "A bulk restore objects request.", "properties": {"allowOverwrite": {"type": "boolean", "description": "If false (default), the restore will not overwrite live objects with the same name at the destination. This means some deleted objects may be skipped. If true, live objects will be overwritten resulting in a noncurrent object (if versioning is enabled). If versioning is not enabled, overwriting the object will result in a soft-deleted object. In either case, if a noncurrent object already exists with the same name, a live version can be written without issue."}, "softDeletedAfterTime": {"type": "string", "description": "Restores only the objects that were soft-deleted after this time.", "format": "date-time"}, "softDeletedBeforeTime": {"type": "string", "description": "Restores only the objects that were soft-deleted before this time.", "format": "date-time"}, "matchGlobs": {"type": "array", "description": "Restores only the objects matching any of the specified glob(s). If this parameter is not specified, all objects will be restored within the specified time range.", "items": {"type": "string"}}, "copySourceAcl": {"type": "boolean", "description": "If true, copies the source object's ACL; otherwise, uses the bucket's default object ACL. The default is false."}}}}, "resources": {"anywhereCaches": {"methods": {"insert": {"id": "storage.anywhereCaches.insert", "path": "b/{bucket}/anywhereCaches", "httpMethod": "POST", "description": "Creates an Anywhere Cache instance.", "parameters": {"bucket": {"type": "string", "description": "Name of the parent bucket.", "required": true, "location": "path"}}, "parameterOrder": ["bucket"], "request": {"$ref": "AnywhereCache"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "update": {"id": "storage.anywhereCaches.update", "path": "b/{bucket}/anywhereCaches/{anywhereCacheId}", "httpMethod": "PATCH", "description": "Updates the config(ttl and admissionPolicy) of an Anywhere Cache instance.", "parameters": {"bucket": {"type": "string", "description": "Name of the parent bucket.", "required": true, "location": "path"}, "anywhereCacheId": {"type": "string", "description": "The ID of requested Anywhere Cache instance.", "required": true, "location": "path"}}, "parameterOrder": ["bucket", "anywhereCacheId"], "request": {"$ref": "AnywhereCache"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"id": "storage.anywhereCaches.get", "path": "b/{bucket}/anywhereCaches/{anywhereCacheId}", "httpMethod": "GET", "description": "Returns the metadata of an Anywhere Cache instance.", "parameters": {"bucket": {"type": "string", "description": "Name of the parent bucket.", "required": true, "location": "path"}, "anywhereCacheId": {"type": "string", "description": "The ID of requested Anywhere Cache instance.", "required": true, "location": "path"}}, "parameterOrder": ["bucket", "anywhereCacheId"], "response": {"$ref": "AnywhereCache"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "list": {"id": "storage.anywhereCaches.list", "path": "b/{bucket}/anywhereCaches", "httpMethod": "GET", "description": "Returns a list of Anywhere Cache instances of the bucket matching the criteria.", "parameters": {"bucket": {"type": "string", "description": "Name of the parent bucket.", "required": true, "location": "path"}, "pageSize": {"type": "integer", "description": "Maximum number of items to return in a single page of responses. Maximum 1000.", "format": "int32", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "AnywhereCaches"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "pause": {"id": "storage.anywhereCaches.pause", "path": "b/{bucket}/anywhereCaches/{anywhereCacheId}/pause", "httpMethod": "POST", "description": "Pauses an Anywhere Cache instance.", "parameters": {"bucket": {"type": "string", "description": "Name of the parent bucket.", "required": true, "location": "path"}, "anywhereCacheId": {"type": "string", "description": "The ID of requested Anywhere Cache instance.", "required": true, "location": "path"}}, "parameterOrder": ["bucket", "anywhereCacheId"], "response": {"$ref": "AnywhereCache"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "resume": {"id": "storage.anywhereCaches.resume", "path": "b/{bucket}/anywhereCaches/{anywhereCacheId}/resume", "httpMethod": "POST", "description": "Resumes a paused or disabled Anywhere Cache instance.", "parameters": {"bucket": {"type": "string", "description": "Name of the parent bucket.", "required": true, "location": "path"}, "anywhereCacheId": {"type": "string", "description": "The ID of requested Anywhere Cache instance.", "required": true, "location": "path"}}, "parameterOrder": ["bucket", "anywhereCacheId"], "response": {"$ref": "AnywhereCache"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "disable": {"id": "storage.anywhereCaches.disable", "path": "b/{bucket}/anywhereCaches/{anywhereCacheId}/disable", "httpMethod": "POST", "description": "Disables an Anywhere Cache instance.", "parameters": {"bucket": {"type": "string", "description": "Name of the parent bucket.", "required": true, "location": "path"}, "anywhereCacheId": {"type": "string", "description": "The ID of requested Anywhere Cache instance.", "required": true, "location": "path"}}, "parameterOrder": ["bucket", "anywhereCacheId"], "response": {"$ref": "AnywhereCache"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}}}, "bucketAccessControls": {"methods": {"delete": {"id": "storage.bucketAccessControls.delete", "path": "b/{bucket}/acl/{entity}", "httpMethod": "DELETE", "description": "Permanently deletes the ACL entry for the specified entity on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "get": {"id": "storage.bucketAccessControls.get", "path": "b/{bucket}/acl/{entity}", "httpMethod": "GET", "description": "Returns the ACL entry for the specified entity on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "response": {"$ref": "BucketAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "insert": {"id": "storage.bucketAccessControls.insert", "path": "b/{bucket}/acl", "httpMethod": "POST", "description": "Creates a new ACL entry on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "BucketAccessControl"}, "response": {"$ref": "BucketAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "list": {"id": "storage.bucketAccessControls.list", "path": "b/{bucket}/acl", "httpMethod": "GET", "description": "Retrieves ACL entries on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "BucketAccessControls"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "patch": {"id": "storage.bucketAccessControls.patch", "path": "b/{bucket}/acl/{entity}", "httpMethod": "PATCH", "description": "Patches an ACL entry on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "request": {"$ref": "BucketAccessControl"}, "response": {"$ref": "BucketAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "update": {"id": "storage.bucketAccessControls.update", "path": "b/{bucket}/acl/{entity}", "httpMethod": "PUT", "description": "Updates an ACL entry on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "request": {"$ref": "BucketAccessControl"}, "response": {"$ref": "BucketAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}}}, "buckets": {"methods": {"delete": {"id": "storage.buckets.delete", "path": "b/{bucket}", "httpMethod": "DELETE", "description": "Permanently deletes an empty bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "If set, only deletes the bucket if its metageneration matches this value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "If set, only deletes the bucket if its metageneration does not match this value.", "format": "int64", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"id": "storage.buckets.get", "path": "b/{bucket}", "httpMethod": "GET", "description": "Returns metadata for the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific soft-deleted version of this bucket instead of the live version. This parameter is required if softDeleted is set to true.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the return of the bucket metadata conditional on whether the bucket's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the return of the bucket metadata conditional on whether the bucket's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit owner, acl and defaultObjectAcl properties."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}, "softDeleted": {"type": "boolean", "description": "If true, returns the soft-deleted bucket. This parameter is required if generation is specified.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "Bucket"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "getIamPolicy": {"id": "storage.buckets.getIamPolicy", "path": "b/{bucket}/iam", "httpMethod": "GET", "description": "Returns an IAM policy for the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "optionsRequestedPolicyVersion": {"type": "integer", "description": "The IAM policy format version to be returned. If the optionsRequestedPolicyVersion is for an older version that doesn't support part of the requested IAM policy, the request fails.", "format": "int32", "minimum": "1", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "insert": {"id": "storage.buckets.insert", "path": "b", "httpMethod": "POST", "description": "Creates a new bucket.", "parameters": {"predefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to this bucket.", "enum": ["authenticatedRead", "private", "projectPrivate", "publicRead", "publicReadWrite"], "enumDescriptions": ["Project team owners get OWNER access, and allAuthenticatedUsers get READER access.", "Project team owners get OWNER access.", "Project team members get access according to their roles.", "Project team owners get OWNER access, and allUsers get READER access.", "Project team owners get OWNER access, and allUsers get WRITER access."], "location": "query"}, "predefinedDefaultObjectAcl": {"type": "string", "description": "Apply a predefined set of default object access controls to this bucket.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "project": {"type": "string", "description": "A valid API project identifier.", "required": true, "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl, unless the bucket resource specifies acl or defaultObjectAcl properties, when it defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit owner, acl and defaultObjectAcl properties."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}, "enableObjectRetention": {"type": "boolean", "description": "When set to true, object retention is enabled for this bucket.", "default": "false", "location": "query"}}, "parameterOrder": ["project"], "request": {"$ref": "Bucket"}, "response": {"$ref": "Bucket"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "list": {"id": "storage.buckets.list", "path": "b", "httpMethod": "GET", "description": "Retrieves a list of buckets for a given project.", "parameters": {"maxResults": {"type": "integer", "description": "Maximum number of buckets to return in a single response. The service will use this parameter or 1,000 items, whichever is smaller.", "default": "1000", "format": "uint32", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query"}, "prefix": {"type": "string", "description": "Filter results to buckets whose names begin with this prefix.", "location": "query"}, "project": {"type": "string", "description": "A valid API project identifier.", "required": true, "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit owner, acl and defaultObjectAcl properties."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}, "softDeleted": {"type": "boolean", "description": "If set to true, only soft-deleted bucket versions are listed as distinct results in order of bucket name and generation number. The default value is false.", "location": "query"}}, "parameterOrder": ["project"], "response": {"$ref": "Buckets"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "lockRetentionPolicy": {"id": "storage.buckets.lockRetentionPolicy", "path": "b/{bucket}/lockRetentionPolicy", "httpMethod": "POST", "description": "Locks retention policy on a bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether bucket's current metageneration matches the given value.", "required": true, "format": "int64", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "ifMetagenerationMatch"], "response": {"$ref": "Bucket"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "patch": {"id": "storage.buckets.patch", "path": "b/{bucket}", "httpMethod": "PATCH", "description": "Patches a bucket. Changes to the bucket will be readable immediately after writing, but configuration changes may take time to propagate.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the return of the bucket metadata conditional on whether the bucket's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the return of the bucket metadata conditional on whether the bucket's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "predefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to this bucket.", "enum": ["authenticatedRead", "private", "projectPrivate", "publicRead", "publicReadWrite"], "enumDescriptions": ["Project team owners get OWNER access, and allAuthenticatedUsers get READER access.", "Project team owners get OWNER access.", "Project team members get access according to their roles.", "Project team owners get OWNER access, and allUsers get READER access.", "Project team owners get OWNER access, and allUsers get WRITER access."], "location": "query"}, "predefinedDefaultObjectAcl": {"type": "string", "description": "Apply a predefined set of default object access controls to this bucket.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit owner, acl and defaultObjectAcl properties."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "Bucket"}, "response": {"$ref": "Bucket"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "setIamPolicy": {"id": "storage.buckets.setIamPolicy", "path": "b/{bucket}/iam", "httpMethod": "PUT", "description": "Updates an IAM policy for the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "Policy"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "testIamPermissions": {"id": "storage.buckets.testIamPermissions", "path": "b/{bucket}/iam/testPermissions", "httpMethod": "GET", "description": "Tests a set of permissions on the given bucket to see which, if any, are held by the caller.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "permissions": {"type": "string", "description": "Permissions to test.", "required": true, "repeated": true, "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "permissions"], "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "update": {"id": "storage.buckets.update", "path": "b/{bucket}", "httpMethod": "PUT", "description": "Updates a bucket. Changes to the bucket will be readable immediately after writing, but configuration changes may take time to propagate.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the return of the bucket metadata conditional on whether the bucket's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the return of the bucket metadata conditional on whether the bucket's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "predefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to this bucket.", "enum": ["authenticatedRead", "private", "projectPrivate", "publicRead", "publicReadWrite"], "enumDescriptions": ["Project team owners get OWNER access, and allAuthenticatedUsers get READER access.", "Project team owners get OWNER access.", "Project team members get access according to their roles.", "Project team owners get OWNER access, and allUsers get READER access.", "Project team owners get OWNER access, and allUsers get WRITER access."], "location": "query"}, "predefinedDefaultObjectAcl": {"type": "string", "description": "Apply a predefined set of default object access controls to this bucket.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit owner, acl and defaultObjectAcl properties."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "Bucket"}, "response": {"$ref": "Bucket"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "restore": {"id": "storage.buckets.restore", "path": "b/{bucket}/restore", "httpMethod": "POST", "description": "Restores a soft-deleted bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket to be restored.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "The specific version of the bucket to be restored.", "required": true, "format": "int64", "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}}, "parameterOrder": ["bucket", "generation"], "response": {"$ref": "Bucket"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed.", "path": "b/{bucket}/operations/{operationId}/cancel", "httpMethod": "POST", "id": "storage.buckets.operations.cancel", "parameterOrder": ["bucket", "operationId"], "parameters": {"bucket": {"description": "The parent bucket of the operation resource.", "location": "path", "required": true, "type": "string"}, "operationId": {"description": "The ID of the operation resource.", "location": "path", "required": true, "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"description": "Gets the latest state of a long-running operation.", "path": "b/{bucket}/operations/{operationId}", "httpMethod": "GET", "id": "storage.buckets.operations.get", "parameterOrder": ["bucket", "operationId"], "parameters": {"bucket": {"description": "The parent bucket of the operation resource.", "location": "path", "required": true, "type": "string"}, "operationId": {"description": "The ID of the operation resource.", "location": "path", "required": true, "type": "string"}}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "list": {"description": "Lists operations that match the specified filter in the request.", "path": "b/{bucket}/operations", "httpMethod": "GET", "id": "storage.buckets.operations.list", "parameterOrder": ["bucket"], "parameters": {"filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "bucket": {"description": "Name of the bucket in which to look for operations.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Maximum number of items to return in a single page of responses. Fewer total results may be returned than requested. The service uses this parameter or 100 items, whichever is smaller.", "minimum": "0", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query", "type": "string"}}, "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}}}, "channels": {"methods": {"stop": {"id": "storage.channels.stop", "path": "channels/stop", "httpMethod": "POST", "description": "Stop watching resources through this channel", "request": {"$ref": "Channel", "parameterName": "resource"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}}}, "defaultObjectAccessControls": {"methods": {"delete": {"id": "storage.defaultObjectAccessControls.delete", "path": "b/{bucket}/defaultObjectAcl/{entity}", "httpMethod": "DELETE", "description": "Permanently deletes the default object ACL entry for the specified entity on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "get": {"id": "storage.defaultObjectAccessControls.get", "path": "b/{bucket}/defaultObjectAcl/{entity}", "httpMethod": "GET", "description": "Returns the default object ACL entry for the specified entity on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "insert": {"id": "storage.defaultObjectAccessControls.insert", "path": "b/{bucket}/defaultObjectAcl", "httpMethod": "POST", "description": "Creates a new default object ACL entry on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "ObjectAccessControl"}, "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "list": {"id": "storage.defaultObjectAccessControls.list", "path": "b/{bucket}/defaultObjectAcl", "httpMethod": "GET", "description": "Retrieves default object ACL entries on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "If present, only return default ACL listing if the bucket's current metageneration matches this value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "If present, only return default ACL listing if the bucket's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "ObjectAccessControls"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "patch": {"id": "storage.defaultObjectAccessControls.patch", "path": "b/{bucket}/defaultObjectAcl/{entity}", "httpMethod": "PATCH", "description": "Patches a default object ACL entry on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "request": {"$ref": "ObjectAccessControl"}, "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "update": {"id": "storage.defaultObjectAccessControls.update", "path": "b/{bucket}/defaultObjectAcl/{entity}", "httpMethod": "PUT", "description": "Updates a default object ACL entry on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "entity"], "request": {"$ref": "ObjectAccessControl"}, "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}}}, "folders": {"methods": {"delete": {"id": "storage.folders.delete", "path": "b/{bucket}/folders/{folder}", "httpMethod": "DELETE", "description": "Permanently deletes a folder. Only applicable to buckets with hierarchical namespace enabled.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the folder resides.", "required": true, "location": "path"}, "folder": {"type": "string", "description": "Name of a folder.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "If set, only deletes the folder if its metageneration matches this value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "If set, only deletes the folder if its metageneration does not match this value.", "format": "int64", "location": "query"}}, "parameterOrder": ["bucket", "folder"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"id": "storage.folders.get", "path": "b/{bucket}/folders/{folder}", "httpMethod": "GET", "description": "Returns metadata for the specified folder. Only applicable to buckets with hierarchical namespace enabled.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the folder resides.", "required": true, "location": "path"}, "folder": {"type": "string", "description": "Name of a folder.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the return of the folder metadata conditional on whether the folder's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the return of the folder metadata conditional on whether the folder's current metageneration does not match the given value.", "format": "int64", "location": "query"}}, "parameterOrder": ["bucket", "folder"], "response": {"$ref": "Folder"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "insert": {"id": "storage.folders.insert", "path": "b/{bucket}/folders", "httpMethod": "POST", "description": "Creates a new folder. Only applicable to buckets with hierarchical namespace enabled.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the folder resides.", "required": true, "location": "path"}, "recursive": {"type": "boolean", "description": "If true, any parent folder which doesn’t exist will be created automatically.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "Folder"}, "response": {"$ref": "Folder"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "list": {"id": "storage.folders.list", "path": "b/{bucket}/folders", "httpMethod": "GET", "description": "Retrieves a list of folders matching the criteria. Only applicable to buckets with hierarchical namespace enabled.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which to look for folders.", "required": true, "location": "path"}, "delimiter": {"type": "string", "description": "Returns results in a directory-like mode. The only supported value is '/'. If set, items will only contain folders that either exactly match the prefix, or are one level below the prefix.", "location": "query"}, "endOffset": {"type": "string", "description": "Filter results to folders whose names are lexicographically before endOffset. If startOffset is also set, the folders listed will have names between startOffset (inclusive) and endOffset (exclusive).", "location": "query"}, "pageSize": {"type": "integer", "description": "Maximum number of items to return in a single page of responses.", "format": "int32", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query"}, "prefix": {"type": "string", "description": "Filter results to folders whose paths begin with this prefix. If set, the value must either be an empty string or end with a '/'.", "location": "query"}, "startOffset": {"type": "string", "description": "Filter results to folders whose names are lexicographically equal to or after startOffset. If endOffset is also set, the folders listed will have names between startOffset (inclusive) and endOffset (exclusive).", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "Folders"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "rename": {"id": "storage.folders.rename", "path": "b/{bucket}/folders/{sourceFolder}/renameTo/folders/{destinationFolder}", "httpMethod": "POST", "description": "Renames a source folder to a destination folder. Only applicable to buckets with hierarchical namespace enabled.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the folders are in.", "required": true, "location": "path"}, "destinationFolder": {"type": "string", "description": "Name of the destination folder.", "required": true, "location": "path"}, "ifSourceMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifSourceMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "sourceFolder": {"type": "string", "description": "Name of the source folder.", "required": true, "location": "path"}}, "parameterOrder": ["bucket", "sourceFolder", "destinationFolder"], "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}}}, "managedFolders": {"methods": {"delete": {"id": "storage.managedFolders.delete", "path": "b/{bucket}/managedFolders/{managedFolder}", "httpMethod": "DELETE", "description": "Permanently deletes a managed folder.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket containing the managed folder.", "required": true, "location": "path"}, "managedFolder": {"type": "string", "description": "The managed folder name/path.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "If set, only deletes the managed folder if its metageneration matches this value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "If set, only deletes the managed folder if its metageneration does not match this value.", "format": "int64", "location": "query"}, "allowNonEmpty": {"type": "boolean", "description": "Allows the deletion of a managed folder even if it is not empty. A managed folder is empty if there are no objects or managed folders that it applies to. Callers must have storage.managedFolders.setIamPolicy permission.", "location": "query"}}, "parameterOrder": ["bucket", "managedFolder"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"id": "storage.managedFolders.get", "path": "b/{bucket}/managedFolders/{managedFolder}", "httpMethod": "GET", "description": "Returns metadata of the specified managed folder.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket containing the managed folder.", "required": true, "location": "path"}, "managedFolder": {"type": "string", "description": "The managed folder name/path.", "required": true, "location": "path"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the return of the managed folder metadata conditional on whether the managed folder's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the return of the managed folder metadata conditional on whether the managed folder's current metageneration does not match the given value.", "format": "int64", "location": "query"}}, "parameterOrder": ["bucket", "managedFolder"], "response": {"$ref": "ManagedFolder"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "getIamPolicy": {"id": "storage.managedFolders.getIamPolicy", "path": "b/{bucket}/managedFolders/{managedFolder}/iam", "httpMethod": "GET", "description": "Returns an IAM policy for the specified managed folder.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket containing the managed folder.", "required": true, "location": "path"}, "optionsRequestedPolicyVersion": {"type": "integer", "description": "The IAM policy format version to be returned. If the optionsRequestedPolicyVersion is for an older version that doesn't support part of the requested IAM policy, the request fails.", "format": "int32", "minimum": "1", "location": "query"}, "managedFolder": {"type": "string", "description": "The managed folder name/path.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "managedFolder"], "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "insert": {"id": "storage.managedFolders.insert", "path": "b/{bucket}/managedFolders", "httpMethod": "POST", "description": "Creates a new managed folder.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket containing the managed folder.", "required": true, "location": "path"}}, "parameterOrder": ["bucket"], "request": {"$ref": "ManagedFolder"}, "response": {"$ref": "ManagedFolder"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "list": {"id": "storage.managedFolders.list", "path": "b/{bucket}/managedFolders", "httpMethod": "GET", "description": "Lists managed folders in the given bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket containing the managed folder.", "required": true, "location": "path"}, "pageSize": {"type": "integer", "description": "Maximum number of items to return in a single page of responses.", "format": "int32", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query"}, "prefix": {"type": "string", "description": "The managed folder name/path prefix to filter the output list of results.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "ManagedFolders"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "setIamPolicy": {"id": "storage.managedFolders.setIamPolicy", "path": "b/{bucket}/managedFolders/{managedFolder}/iam", "httpMethod": "PUT", "description": "Updates an IAM policy for the specified managed folder.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket containing the managed folder.", "required": true, "location": "path"}, "managedFolder": {"type": "string", "description": "The managed folder name/path.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "managedFolder"], "request": {"$ref": "Policy"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "testIamPermissions": {"id": "storage.managedFolders.testIamPermissions", "path": "b/{bucket}/managedFolders/{managedFolder}/iam/testPermissions", "httpMethod": "GET", "description": "Tests a set of permissions on the given managed folder to see which, if any, are held by the caller.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket containing the managed folder.", "required": true, "location": "path"}, "managedFolder": {"type": "string", "description": "The managed folder name/path.", "required": true, "location": "path"}, "permissions": {"type": "string", "description": "Permissions to test.", "required": true, "repeated": true, "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "managedFolder", "permissions"], "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}}}, "notifications": {"methods": {"delete": {"id": "storage.notifications.delete", "path": "b/{bucket}/notificationConfigs/{notification}", "httpMethod": "DELETE", "description": "Permanently deletes a notification subscription.", "parameters": {"bucket": {"type": "string", "description": "The parent bucket of the notification.", "required": true, "location": "path"}, "notification": {"type": "string", "description": "ID of the notification to delete.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "notification"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"id": "storage.notifications.get", "path": "b/{bucket}/notificationConfigs/{notification}", "httpMethod": "GET", "description": "View a notification configuration.", "parameters": {"bucket": {"type": "string", "description": "The parent bucket of the notification.", "required": true, "location": "path"}, "notification": {"type": "string", "description": "Notification ID", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "notification"], "response": {"$ref": "Notification"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "insert": {"id": "storage.notifications.insert", "path": "b/{bucket}/notificationConfigs", "httpMethod": "POST", "description": "Creates a notification subscription for a given bucket.", "parameters": {"bucket": {"type": "string", "description": "The parent bucket of the notification.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "Notification"}, "response": {"$ref": "Notification"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "list": {"id": "storage.notifications.list", "path": "b/{bucket}/notificationConfigs", "httpMethod": "GET", "description": "Retrieves a list of notification subscriptions for a given bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of a Google Cloud Storage bucket.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "Notifications"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}}}, "objectAccessControls": {"methods": {"delete": {"id": "storage.objectAccessControls.delete", "path": "b/{bucket}/o/{object}/acl/{entity}", "httpMethod": "DELETE", "description": "Permanently deletes the ACL entry for the specified entity on the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object", "entity"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "get": {"id": "storage.objectAccessControls.get", "path": "b/{bucket}/o/{object}/acl/{entity}", "httpMethod": "GET", "description": "Returns the ACL entry for the specified entity on the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object", "entity"], "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "insert": {"id": "storage.objectAccessControls.insert", "path": "b/{bucket}/o/{object}/acl", "httpMethod": "POST", "description": "Creates a new ACL entry on the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "request": {"$ref": "ObjectAccessControl"}, "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "list": {"id": "storage.objectAccessControls.list", "path": "b/{bucket}/o/{object}/acl", "httpMethod": "GET", "description": "Retrieves ACL entries on the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "response": {"$ref": "ObjectAccessControls"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "patch": {"id": "storage.objectAccessControls.patch", "path": "b/{bucket}/o/{object}/acl/{entity}", "httpMethod": "PATCH", "description": "Patches an ACL entry on the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object", "entity"], "request": {"$ref": "ObjectAccessControl"}, "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "update": {"id": "storage.objectAccessControls.update", "path": "b/{bucket}/o/{object}/acl/{entity}", "httpMethod": "PUT", "description": "Updates an ACL entry on the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of a bucket.", "required": true, "location": "path"}, "entity": {"type": "string", "description": "The entity holding the permission. Can be user-userId, user-emailAddress, group-groupId, group-emailAddress, allUsers, or allAuthenticatedUsers.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object", "entity"], "request": {"$ref": "ObjectAccessControl"}, "response": {"$ref": "ObjectAccessControl"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}}}, "objects": {"methods": {"compose": {"id": "storage.objects.compose", "path": "b/{destinationBucket}/o/{destinationObject}/compose", "httpMethod": "POST", "description": "Concatenates a list of existing objects into a new object in the same bucket.", "parameters": {"destinationBucket": {"type": "string", "description": "Name of the bucket containing the source objects. The destination object is stored in this bucket.", "required": true, "location": "path"}, "destinationObject": {"type": "string", "description": "Name of the new object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "destinationPredefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to the destination object.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "kmsKeyName": {"type": "string", "description": "Resource name of the Cloud KMS key, of the form projects/my-project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be used to encrypt the object. Overrides the object metadata's kms_key_name value, if any.", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["destinationBucket", "destinationObject"], "request": {"$ref": "ComposeRequest"}, "response": {"$ref": "Object"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "copy": {"id": "storage.objects.copy", "path": "b/{sourceBucket}/o/{sourceObject}/copyTo/b/{destinationBucket}/o/{destinationObject}", "httpMethod": "POST", "description": "Copies a source object to a destination object. Optionally overrides metadata.", "parameters": {"destinationBucket": {"type": "string", "description": "Name of the bucket in which to store the new object. Overrides the provided object metadata's bucket value, if any.For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "destinationKmsKeyName": {"type": "string", "description": "Resource name of the Cloud KMS key, of the form projects/my-project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be used to encrypt the object. Overrides the object metadata's kms_key_name value, if any.", "location": "query"}, "destinationObject": {"type": "string", "description": "Name of the new object. Required when the object metadata is not otherwise provided. Overrides the object metadata's name value, if any.", "required": true, "location": "path"}, "destinationPredefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to the destination object.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the destination object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the destination object's current generation does not match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the destination object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the destination object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "ifSourceGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current generation matches the given value.", "format": "int64", "location": "query"}, "ifSourceGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current generation does not match the given value.", "format": "int64", "location": "query"}, "ifSourceMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifSourceMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl, unless the object resource specifies the acl property, when it defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "sourceBucket": {"type": "string", "description": "Name of the bucket in which to find the source object.", "required": true, "location": "path"}, "sourceGeneration": {"type": "string", "description": "If present, selects a specific revision of the source object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "sourceObject": {"type": "string", "description": "Name of the source object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["sourceBucket", "sourceObject", "destinationBucket", "destinationObject"], "request": {"$ref": "Object"}, "response": {"$ref": "Object"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "delete": {"id": "storage.objects.delete", "path": "b/{bucket}/o/{object}", "httpMethod": "DELETE", "description": "Deletes an object and its metadata. Deletions are permanent if versioning is not enabled for the bucket, or if the generation parameter is used.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, permanently deletes a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation does not match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"id": "storage.objects.get", "path": "b/{bucket}/o/{object}", "httpMethod": "GET", "description": "Retrieves an object or its metadata.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation does not match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}, "softDeleted": {"type": "boolean", "description": "If true, only soft-deleted object versions will be listed. The default is false. For more information, see Soft Delete.", "location": "query"}, "restoreToken": {"type": "string", "description": "If you have enabled hierarchical namespace on your bucket and are working with soft-deleted objects, the restoreToken, a universally unique identifier (UUID), along with the object's name and generation value, uniquely identifies the object.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "response": {"$ref": "Object"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"], "supportsMediaDownload": true, "useMediaDownloadService": true}, "getIamPolicy": {"id": "storage.objects.getIamPolicy", "path": "b/{bucket}/o/{object}/iam", "httpMethod": "GET", "description": "Returns an IAM policy for the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "insert": {"id": "storage.objects.insert", "path": "b/{bucket}/o", "httpMethod": "POST", "description": "Stores a new object and metadata.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which to store the new object. Overrides the provided object metadata's bucket value, if any.", "required": true, "location": "path"}, "contentEncoding": {"type": "string", "description": "If set, sets the contentEncoding property of the final object to this value. Setting this parameter is equivalent to setting the contentEncoding metadata property. This can be useful when uploading an object with uploadType=media to indicate the encoding of the content being uploaded.", "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation does not match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "kmsKeyName": {"type": "string", "description": "Resource name of the Cloud KMS key, of the form projects/my-project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be used to encrypt the object. Overrides the object metadata's kms_key_name value, if any.", "location": "query"}, "name": {"type": "string", "description": "Name of the object. Required when the object metadata is not otherwise provided. Overrides the object metadata's name value, if any. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "location": "query"}, "predefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to this object.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl, unless the object resource specifies the acl property, when it defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "Object"}, "response": {"$ref": "Object"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"], "supportsMediaUpload": true, "mediaUpload": {"accept": ["*/*"], "protocols": {"simple": {"multipart": true, "path": "/upload/storage/v1/b/{bucket}/o"}, "resumable": {"multipart": true, "path": "/resumable/upload/storage/v1/b/{bucket}/o"}}}}, "list": {"id": "storage.objects.list", "path": "b/{bucket}/o", "httpMethod": "GET", "description": "Retrieves a list of objects matching the criteria.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which to look for objects.", "required": true, "location": "path"}, "delimiter": {"type": "string", "description": "Returns results in a directory-like mode. items will contain only objects whose names, aside from the prefix, do not contain delimiter. Objects whose names, aside from the prefix, contain delimiter will have their name, truncated after the delimiter, returned in prefixes. Duplicate prefixes are omitted.", "location": "query"}, "endOffset": {"type": "string", "description": "Filter results to objects whose names are lexicographically before endOffset. If startOffset is also set, the objects listed will have names between startOffset (inclusive) and endOffset (exclusive).", "location": "query"}, "includeTrailingDelimiter": {"type": "boolean", "description": "If true, objects that end in exactly one instance of delimiter will have their metadata included in items in addition to prefixes.", "location": "query"}, "maxResults": {"type": "integer", "description": "Maximum number of items plus prefixes to return in a single page of responses. As duplicate prefixes are omitted, fewer total results may be returned than requested. The service will use this parameter or 1,000 items, whichever is smaller.", "default": "1000", "format": "uint32", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query"}, "prefix": {"type": "string", "description": "Filter results to objects whose names begin with this prefix.", "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "startOffset": {"type": "string", "description": "Filter results to objects whose names are lexicographically equal to or after startOffset. If endOffset is also set, the objects listed will have names between startOffset (inclusive) and endOffset (exclusive).", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}, "versions": {"type": "boolean", "description": "If true, lists all versions of an object as distinct results. The default is false. For more information, see Object Versioning.", "location": "query"}, "matchGlob": {"type": "string", "description": "Filter results to objects and prefixes that match this glob pattern.", "location": "query"}, "softDeleted": {"type": "boolean", "description": "If true, only soft-deleted object versions will be listed. The default is false. For more information, see Soft Delete.", "location": "query"}, "includeFoldersAsPrefixes": {"type": "boolean", "description": "Only applicable if delimiter is set to '/'. If true, will also include folders and managed folders (besides objects) in the returned prefixes.", "location": "query"}}, "parameterOrder": ["bucket"], "response": {"$ref": "Objects"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"], "supportsSubscription": true}, "patch": {"id": "storage.objects.patch", "path": "b/{bucket}/o/{object}", "httpMethod": "PATCH", "description": "Patches an object's metadata.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation does not match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "overrideUnlockedRetention": {"type": "boolean", "description": "Must be true to remove the retention configuration, reduce its unlocked retention period, or change its mode from unlocked to locked.", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "predefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to this object.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request, for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "request": {"$ref": "Object"}, "response": {"$ref": "Object"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "rewrite": {"id": "storage.objects.rewrite", "path": "b/{sourceBucket}/o/{sourceObject}/rewriteTo/b/{destinationBucket}/o/{destinationObject}", "httpMethod": "POST", "description": "Rewrites a source object to a destination object. Optionally overrides metadata.", "parameters": {"destinationBucket": {"type": "string", "description": "Name of the bucket in which to store the new object. Overrides the provided object metadata's bucket value, if any.", "required": true, "location": "path"}, "destinationKmsKeyName": {"type": "string", "description": "Resource name of the Cloud KMS key, of the form projects/my-project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be used to encrypt the object. Overrides the object metadata's kms_key_name value, if any.", "location": "query"}, "destinationObject": {"type": "string", "description": "Name of the new object. Required when the object metadata is not otherwise provided. Overrides the object metadata's name value, if any. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "destinationPredefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to the destination object.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation does not match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the destination object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the destination object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "ifSourceGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current generation matches the given value.", "format": "int64", "location": "query"}, "ifSourceGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current generation does not match the given value.", "format": "int64", "location": "query"}, "ifSourceMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifSourceMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the source object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "maxBytesRewrittenPerCall": {"type": "string", "description": "The maximum number of bytes that will be rewritten per rewrite request. Most callers shouldn't need to specify this parameter - it is primarily in place to support testing. If specified the value must be an integral multiple of 1 MiB (1048576). Also, this only applies to requests where the source and destination span locations and/or storage classes. Finally, this value must not change across rewrite calls else you'll get an error that the rewriteToken is invalid.", "format": "int64", "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl, unless the object resource specifies the acl property, when it defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "rewriteToken": {"type": "string", "description": "Include this field (from the previous rewrite response) on each rewrite request after the first one, until the rewrite response 'done' flag is true. Calls that provide a rewriteToken can omit all other request fields, but if included those fields must match the values provided in the first rewrite request.", "location": "query"}, "sourceBucket": {"type": "string", "description": "Name of the bucket in which to find the source object.", "required": true, "location": "path"}, "sourceGeneration": {"type": "string", "description": "If present, selects a specific revision of the source object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "sourceObject": {"type": "string", "description": "Name of the source object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["sourceBucket", "sourceObject", "destinationBucket", "destinationObject"], "request": {"$ref": "Object"}, "response": {"$ref": "RewriteResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "setIamPolicy": {"id": "storage.objects.setIamPolicy", "path": "b/{bucket}/o/{object}/iam", "httpMethod": "PUT", "description": "Updates an IAM policy for the specified object.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "request": {"$ref": "Policy"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "testIamPermissions": {"id": "storage.objects.testIamPermissions", "path": "b/{bucket}/o/{object}/iam/testPermissions", "httpMethod": "GET", "description": "Tests a set of permissions on the given object to see which, if any, are held by the caller.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "permissions": {"type": "string", "description": "Permissions to test.", "required": true, "repeated": true, "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object", "permissions"], "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}, "update": {"id": "storage.objects.update", "path": "b/{bucket}/o/{object}", "httpMethod": "PUT", "description": "Updates an object's metadata.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "If present, selects a specific revision of this object (as opposed to the latest version, the default).", "format": "int64", "location": "query"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current generation does not match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's current metageneration does not match the given value.", "format": "int64", "location": "query"}, "overrideUnlockedRetention": {"type": "boolean", "description": "Must be true to remove the retention configuration, reduce its unlocked retention period, or change its mode from unlocked to locked.", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see [Encoding URI Path Parts](https://cloud.google.com/storage/docs/request-endpoints#encoding).", "required": true, "location": "path"}, "predefinedAcl": {"type": "string", "description": "Apply a predefined set of access controls to this object.", "enum": ["authenticatedRead", "bucketOwnerFullControl", "bucketOwnerRead", "private", "projectPrivate", "publicRead"], "enumDescriptions": ["Object owner gets OWNER access, and allAuthenticatedUsers get READER access.", "Object owner gets OWNER access, and project team owners get OWNER access.", "Object owner gets OWNER access, and project team owners get READER access.", "Object owner gets OWNER access.", "Object owner gets OWNER access, and project team members get access according to their roles.", "Object owner gets OWNER access, and allUsers get READER access."], "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}}, "parameterOrder": ["bucket", "object"], "request": {"$ref": "Object"}, "response": {"$ref": "Object"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "watchAll": {"id": "storage.objects.watchAll", "path": "b/{bucket}/o/watch", "httpMethod": "POST", "description": "Watch for changes on all objects in a bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which to look for objects.", "required": true, "location": "path"}, "delimiter": {"type": "string", "description": "Returns results in a directory-like mode. items will contain only objects whose names, aside from the prefix, do not contain delimiter. Objects whose names, aside from the prefix, contain delimiter will have their name, truncated after the delimiter, returned in prefixes. Duplicate prefixes are omitted.", "location": "query"}, "endOffset": {"type": "string", "description": "Filter results to objects whose names are lexicographically before endOffset. If startOffset is also set, the objects listed will have names between startOffset (inclusive) and endOffset (exclusive).", "location": "query"}, "includeTrailingDelimiter": {"type": "boolean", "description": "If true, objects that end in exactly one instance of delimiter will have their metadata included in items in addition to prefixes.", "location": "query"}, "maxResults": {"type": "integer", "description": "Maximum number of items plus prefixes to return in a single page of responses. As duplicate prefixes are omitted, fewer total results may be returned than requested. The service will use this parameter or 1,000 items, whichever is smaller.", "default": "1000", "format": "uint32", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query"}, "prefix": {"type": "string", "description": "Filter results to objects whose names begin with this prefix.", "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to noAcl.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "startOffset": {"type": "string", "description": "Filter results to objects whose names are lexicographically equal to or after startOffset. If endOffset is also set, the objects listed will have names between startOffset (inclusive) and endOffset (exclusive).", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}, "versions": {"type": "boolean", "description": "If true, lists all versions of an object as distinct results. The default is false. For more information, see Object Versioning.", "location": "query"}}, "parameterOrder": ["bucket"], "request": {"$ref": "Channel", "parameterName": "resource"}, "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"], "supportsSubscription": true}, "restore": {"id": "storage.objects.restore", "path": "b/{bucket}/o/{object}/restore", "httpMethod": "POST", "description": "Restores a soft-deleted object.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}, "generation": {"type": "string", "description": "Selects a specific revision of this object.", "required": true, "format": "int64", "location": "query"}, "object": {"type": "string", "description": "Name of the object. For information about how to URL encode object names to be path safe, see Encoding URI Path Parts.", "required": true, "location": "path"}, "ifGenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's one live generation matches the given value. Setting to 0 makes the operation succeed only if there are no live versions of the object.", "format": "int64", "location": "query"}, "ifGenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether none of the object's live generations match the given value. If no live object exists, the precondition fails. Setting to 0 makes the operation succeed only if there is a live version of the object.", "format": "int64", "location": "query"}, "ifMetagenerationMatch": {"type": "string", "description": "Makes the operation conditional on whether the object's one live metageneration matches the given value.", "format": "int64", "location": "query"}, "ifMetagenerationNotMatch": {"type": "string", "description": "Makes the operation conditional on whether none of the object's live metagenerations match the given value.", "format": "int64", "location": "query"}, "copySourceAcl": {"type": "boolean", "description": "If true, copies the source object's ACL; otherwise, uses the bucket's default object ACL. The default is false.", "location": "query"}, "projection": {"type": "string", "description": "Set of properties to return. Defaults to full.", "enum": ["full", "noAcl"], "enumDescriptions": ["Include all properties.", "Omit the owner, acl property."], "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request. Required for Requester Pays buckets.", "location": "query"}, "restoreToken": {"type": "string", "description": "The restoreToken is required to restore a soft-deleted object only if its name and generation value do not uniquely identify it.", "location": "query"}}, "parameterOrder": ["bucket", "object", "generation"], "response": {"$ref": "Object"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "bulkRestore": {"id": "storage.objects.bulkRestore", "path": "b/{bucket}/o/bulkRestore", "httpMethod": "POST", "description": "Initiates a long-running bulk restore operation on the specified bucket.", "parameters": {"bucket": {"type": "string", "description": "Name of the bucket in which the object resides.", "required": true, "location": "path"}}, "parameterOrder": ["bucket"], "request": {"$ref": "BulkRestoreObjectsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}}}, "projects": {"resources": {"hmacKeys": {"methods": {"create": {"id": "storage.projects.hmacKeys.create", "path": "projects/{projectId}/hmacKeys", "httpMethod": "POST", "description": "Creates a new HMAC key for the specified service account.", "parameters": {"projectId": {"type": "string", "description": "Project ID owning the service account.", "required": true, "location": "path"}, "serviceAccountEmail": {"type": "string", "description": "Email address of the service account.", "required": true, "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}}, "parameterOrder": ["projectId", "serviceAccountEmail"], "response": {"$ref": "Hmac<PERSON>ey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}, "delete": {"id": "storage.projects.hmacKeys.delete", "path": "projects/{projectId}/hmacKeys/{accessId}", "httpMethod": "DELETE", "description": "Deletes an HMAC key.", "parameters": {"accessId": {"type": "string", "description": "Name of the HMAC key to be deleted.", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID owning the requested key", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}}, "parameterOrder": ["projectId", "accessId"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_write"]}, "get": {"id": "storage.projects.hmacKeys.get", "path": "projects/{projectId}/hmacKeys/{accessId}", "httpMethod": "GET", "description": "Retrieves an HMAC key's metadata", "parameters": {"accessId": {"type": "string", "description": "Name of the HMAC key.", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID owning the service account of the requested key.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}}, "parameterOrder": ["projectId", "accessId"], "response": {"$ref": "HmacKeyMetadata"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only"]}, "list": {"id": "storage.projects.hmacKeys.list", "path": "projects/{projectId}/hmacKeys", "httpMethod": "GET", "description": "Retrieves a list of HMAC keys matching the criteria.", "parameters": {"maxResults": {"type": "integer", "description": "Maximum number of items to return in a single page of responses. The service uses this parameter or 250 items, whichever is smaller. The max number of items per page will also be limited by the number of distinct service accounts in the response. If the number of service accounts in a single response is too high, the page will truncated and a next page token will be returned.", "default": "250", "format": "uint32", "minimum": "0", "location": "query"}, "pageToken": {"type": "string", "description": "A previously-returned page token representing part of the larger set of results to view.", "location": "query"}, "projectId": {"type": "string", "description": "Name of the project in which to look for HMAC keys.", "required": true, "location": "path"}, "serviceAccountEmail": {"type": "string", "description": "If present, only keys for the given service account are returned.", "location": "query"}, "showDeletedKeys": {"type": "boolean", "description": "Whether or not to show keys in the DELETED state.", "location": "query"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}}, "parameterOrder": ["projectId"], "response": {"$ref": "HmacKeysMetadata"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only"]}, "update": {"id": "storage.projects.hmacKeys.update", "path": "projects/{projectId}/hmacKeys/{accessId}", "httpMethod": "PUT", "description": "Updates the state of an HMAC key. See the HMAC Key resource descriptor for valid states.", "parameters": {"accessId": {"type": "string", "description": "Name of the HMAC key being updated.", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID owning the service account of the updated key.", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}}, "parameterOrder": ["projectId", "accessId"], "request": {"$ref": "HmacKeyMetadata"}, "response": {"$ref": "HmacKeyMetadata"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control"]}}}, "serviceAccount": {"methods": {"get": {"id": "storage.projects.serviceAccount.get", "path": "projects/{projectId}/serviceAccount", "httpMethod": "GET", "description": "Get the email address of this project's Google Cloud Storage service account.", "parameters": {"projectId": {"type": "string", "description": "Project ID", "required": true, "location": "path"}, "userProject": {"type": "string", "description": "The project to be billed for this request.", "location": "query"}}, "parameterOrder": ["projectId"], "response": {"$ref": "ServiceAccount"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"]}}}}}}, "revision": "********", "etag": "\"****************************************\""}