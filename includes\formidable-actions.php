<?php

// Register the Firebase Push action with Formidable Forms
add_action('frm_registered_form_actions', 'register_firebase_push_action');

function register_firebase_push_action($actions)
{
    $actions['firebase_push'] = 'FrmFirebasePushAction';
    return $actions;
}

class FrmFirebasePushAction extends FrmFormAction
{
    public $key = 'firebase_push'; // Unique key for the action
    public $label = 'Q-Notify'; // Label for the action displayed in the UI
    public $priority = 11; // Priority for the action
    public $event = array(); // Declare event property to prevent deprecation warning

    public function __construct()
    {
        // Define action options
        $action_ops = array(
            'classes' => 'dashicons dashicons-bell', // Dashicon class for the action
            'limit' => 99, // Limit for the number of actions
            'active' => true, // Whether the action is active
            'priority' => 11, // Priority for the action
            'event' => array('create', 'update'), // Events that trigger this action
            'color' => 'var(--purple)', // Color for the action
        );

        $this->event = $action_ops['event']; // Set the event property

        // Call parent constructor
        parent::__construct('firebase_push', __('Q-Notify', 'formidable-firebase-push'), $action_ops);

        // Add hooks for create and update events
        add_action('frm_after_create_entry', array($this, 'maybe_trigger_create'), 30, 2);
        add_action('frm_after_update_entry', array($this, 'maybe_trigger_update'), 30, 2);
    }

    /**
     * Render the form settings for the action.
     *
     * @param object $instance The action instance.
     * @param array $args Arguments passed to the form.
     */
    public function form($instance, $args = array())
    {
        $form = $args['form'];
        $action = $instance;

        $options = $action->post_content;

        include(Q_PLUGIN_DIR . 'includes/templates/form-action-settings.php');
    }

    /**
     * Get the JavaScript settings for the form action.
     *
     * @return array The settings for the form action in JavaScript.
     */
    public function form_action_js()
    {
        return array(
            'key' => $this->key,
            'label' => $this->label,
            'options' => array(
                'title' => array(
                    'type' => 'text',
                    'label' => __('Push Notification Title', 'formidable-firebase-push'),
                    'required' => true
                ),
                'message' => array(
                    'type' => 'textarea',
                    'label' => __('Push Notification Message', 'formidable-firebase-push'),
                    'required' => true
                ),
                'notification_image' => array(
                    'type' => 'text',
                    'label' => __('Notification Image', 'formidable-firebase-push'),
                    'description' => __('Enter an image URL or use a [field_id] shortcode for dynamic images', 'formidable-firebase-push')
                ),
                'subscriber_email' => array(
                    'type' => 'email',
                    'label' => __('Subscriber Emails', 'formidable-firebase-push'),
                    'required' => true,
                    'multiple' => true,
                    'description' => __('Enter multiple subscriber emails separated by commas.', 'formidable-firebase-push')
                )
            ),
        );
    }

    /**
     * Save the form action settings.
     *
     * @param array $settings The settings to save.
     * @return array The sanitized settings.
     */
    public function save_form_action($settings)
    {
        $settings['title'] = isset($settings['title']) ? sanitize_text_field($settings['title']) : '';
        $settings['message'] = isset($settings['message']) ? sanitize_textarea_field($settings['message']) : '';
        if (isset($settings['subscriber_email'])) {
            $emails = is_array($settings['subscriber_email']) ?
                $settings['subscriber_email'] :
                explode(',', $settings['subscriber_email']);

            $settings['subscriber_email'] = array_filter(array_map(function ($email) {
                return sanitize_email(trim($email));
            }, $emails));
        } else {
            $settings['subscriber_email'] = array();
        }

        if (!isset($settings['event'])) {
            $settings['event'] = 'create';
        }

        // Save retrigger settings
        $settings['enable_retrigger'] = isset($settings['enable_retrigger']) ? 1 : 0;
        $settings['retrigger_delay'] = isset($settings['retrigger_delay']) ?
            intval($settings['retrigger_delay']) : 24;
        $settings['retrigger_max_attempts'] = isset($settings['retrigger_max_attempts']) ?
            intval($settings['retrigger_max_attempts']) : 3;

        // Validate retrigger settings
        if ($settings['retrigger_delay'] < 1) {
            $settings['retrigger_delay'] = 1;
        } elseif ($settings['retrigger_delay'] > 72) {
            $settings['retrigger_delay'] = 72;
        }

        if ($settings['retrigger_max_attempts'] < 1) {
            $settings['retrigger_max_attempts'] = 1;
        } elseif ($settings['retrigger_max_attempts'] > 10) {
            $settings['retrigger_max_attempts'] = 10;
        }

        return $settings;
    }



    /**
     * Trigger the form action.
     *
     * @param object $action The action instance.
     * @param object $entry The entry that triggered the action.
     * @param object $form The form associated with the entry.
     * @return bool True if the action was triggered successfully, false otherwise.
     */
    private function trigger_action($action, $entry, $form)
    {
        try {

            // Get notification details
            $options = $action->post_content;
            $title = isset($options['title']) ? $options['title'] : '';
            $message = isset($options['message']) ? $options['message'] : '';
            $notification_image = isset($options['notification_image']) ? $options['notification_image'] : '';
            $subscriber_emails = isset($options['subscriber_email']) ? $options['subscriber_email'] : array();
            if (is_string($subscriber_emails)) {
                $subscriber_emails = explode(',', $subscriber_emails);
                $subscriber_emails = array_map('trim', $subscriber_emails);
            } elseif (is_array($subscriber_emails)) {
                $temp_emails = array();
                foreach ($subscriber_emails as $email) {
                    if (is_string($email)) {
                        $email = explode(',', $email);
                        $temp_emails = array_merge($temp_emails, $email);
                    } else {
                        $temp_emails[] = $email;
                    }
                }
                $subscriber_emails = array_map('trim', $temp_emails);
            }

            // Process shortcodes for subscriber emails
            $processed_emails = array();
            foreach ($subscriber_emails as $email) {
                $shortcode_result = do_shortcode($email);
                $exploded_emails = explode(',', $shortcode_result);
                $processed_emails = array_merge($processed_emails, $exploded_emails);
            }
            $subscriber_emails = $processed_emails;
            error_log('Processed subscriber emails in trigger: ' . print_r($subscriber_emails, true));

            // Process all shortcodes (WordPress and Formidable)
            $notification_data = array(
                'title' => $title,
                'message' => $message,
                'image' => $notification_image
            );

            foreach ($notification_data as $key => &$value) {
                $value = (string) $value; // Ensure string type
                $value = do_shortcode($value);
                if (class_exists('FrmEntryShortcodes') && $form && $entry) {
                    $value = FrmEntryShortcodes::replace_field_shortcodes((string) $value, $form, $entry);
                }
            }

            // Validate and sanitize processed data
            if (empty($notification_data['title']) || empty($notification_data['message']) || empty(array_filter($subscriber_emails))) {
                error_log('Missing required notification data');
                return false;
            }

            $title = sanitize_text_field($notification_data['title']);
            $message = sanitize_textarea_field($notification_data['message']);
            $notification_image = esc_url_raw($notification_data['image']);

            foreach ($subscriber_emails as $subscriber_email) {
                $subscriber_email = sanitize_email(trim($subscriber_email));

                // Get the user ID by email
                $user = get_user_by('email', $subscriber_email);
                if ($user) {
                    $token = get_user_meta($user->ID, 'q_push_token', true);
                    if (!empty($token)) {
                        // Add retrigger settings to the notification data
                        $additional_data = [
                            'form_id' => $form->id,
                            'action_id' => $action->ID
                        ];

                        // Include retrigger settings if enabled
                        if (isset($options['enable_retrigger']) && $options['enable_retrigger']) {
                            $additional_data['enable_retrigger'] = true;
                            $additional_data['retrigger_delay'] = isset($options['retrigger_delay']) ?
                                intval($options['retrigger_delay']) : 24;
                            $additional_data['retrigger_max_attempts'] = isset($options['retrigger_max_attempts']) ?
                                intval($options['retrigger_max_attempts']) : 3;
                            $additional_data['retrigger_attempt'] = 0; // Initial attempt
                        }

                        $result = q_send_push_notification(
                            $token,
                            $title,
                            $message,
                            $notification_image,
                            false,
                            'info',
                            $additional_data
                        );

                        // Remove this tracking block since it's already handled in q_send_push_notification
                        /*if ($result) {
                            // Track the notification
                            $notification_id = wp_generate_uuid4();
                            $track_result = q_track_notification(
                                $notification_id,
                                'sent',
                                [
                                    'title' => $title,
                                    'message' => $message,
                                    'image' => $notification_image,
                                    'subscriber_email' => $subscriber_email
                                ],
                                $form->id
                            );

                            error_log(sprintf(
                                'Notification tracking: ID=%s, Form=%d, Result=%s',
                                $notification_id,
                                $form->id,
                                $track_result ? 'success' : 'failed'
                            ));
                        }*/
                    }
                } else {
                    error_log("Trigger Action - No user found with email $subscriber_email");
                }
            }

            return true;
        } catch (Exception $e) {
            error_log('ERROR in trigger_action: ' . $e->getMessage());
            error_log($e->getTraceAsString());
            error_log("Error in sending notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle create event.
     *
     * @param int $entry_id The ID of the entry created.
     * @param int $form_id The ID of the form associated with the entry.
     */
    public function maybe_trigger_create($entry_id, $form_id)
    {
        $this->maybe_trigger_action($entry_id, $form_id, 'create');
    }

    /**
     * Handle update event.
     *
     * @param int $entry_id The ID of the entry updated.
     * @param int $form_id The ID of the form associated with the entry.
     */
    public function maybe_trigger_update($entry_id, $form_id)
    {
        $this->maybe_trigger_action($entry_id, $form_id, 'update');
    }

    /**
     * Common handler for both create and update events.
     *
     * @param int $entry_id The ID of the entry.
     * @param int $form_id The ID of the form.
     * @param string $event The event type (create or update).
     */
    private function maybe_trigger_action($entry_id, $form_id, $event)
    {
        // Generate a unique lock key for this submission
        $lock_key = 'q_push_lock_' . md5($form_id . $entry_id . $event);

        // Check if this submission is already being processed
        if (get_transient($lock_key)) {
            error_log("Duplicate form submission prevented for entry: $entry_id");
            return;
        }

        // Set a lock for 30 seconds
        set_transient($lock_key, true, 30);

        $entry = false;
        $form = false;

        // Get the entry
        if (class_exists('FrmEntry')) {
            $entry = FrmEntry::getOne($entry_id, true);
        } else {
            error_log("FrmEntry class not found");
        }

        // Get the form
        if (class_exists('FrmForm')) {
            $form = FrmForm::getOne($form_id);
            if (!$form) {
                return;
            }
            error_log("Found form: " . print_r($form, true));
        } else {
            error_log("FrmForm class not found");
        }

        // Get all actions for this form
        $actions = FrmFormAction::get_action_for_form($form_id, 'firebase_push');

        foreach ($actions as $action) {

            // Check if this action should run for this event
            if (isset($action->post_content['event']) && in_array($event, (array) $action->post_content['event'])) {
                error_log('Triggering action {$action->ID} for $event event');
                try {
                    $result = $this->trigger_action($action, $entry, $form);
                    error_log("Action trigger result: " . ($result ? 'success' : 'failed'));
                } catch (Exception $e) {
                    error_log("Error triggering action: " . $e->getMessage());
                }
            } else {
                error_log("Action {$action->ID} not configured for $event event");
                error_log("Action event setting: " . print_r($action->post_content['event'], true));
            }
        }
    }




}

// Register the form action
add_action('frm_registered_form_actions', 'q_register_form_action');

/**
 * Register the Q Pusher form action.
 *
 * @param array $actions The current actions.
 * @return array The updated actions.
 */
function q_register_form_action($actions)
{

    if (!isset($actions['firebase_push'])) {
        $actions['firebase_push'] = 'FrmFirebasePushAction';

    }

    return $actions;
}



