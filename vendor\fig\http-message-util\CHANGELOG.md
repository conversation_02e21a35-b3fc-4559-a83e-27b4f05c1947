# Changelog

All notable changes to this project will be documented in this file, in reverse chronological order by release.

## 1.1.5 - 2020-11-24

### Added

- [#19](https://github.com/php-fig/http-message-util/pull/19) adds support for PHP 8.

### Changed

- Nothing.

### Deprecated

- Nothing.

### Removed

- Nothing.

### Fixed

- Nothing.

## 1.1.4 - 2020-02-05

### Added

- Nothing.

### Changed

- Nothing.

### Deprecated

- Nothing.

### Removed

- [#15](https://github.com/php-fig/http-message-util/pull/15) removes the dependency on psr/http-message, as it is not technically necessary for usage of this package.

### Fixed

- Nothing.

## 1.1.3 - 2018-11-19

### Added

- [#10](https://github.com/php-fig/http-message-util/pull/10) adds the constants `StatusCodeInterface::STATUS_EARLY_HINTS` (103) and
  `StatusCodeInterface::STATUS_TOO_EARLY` (425).

### Changed

- Nothing.

### Deprecated

- Nothing.

### Removed

- Nothing.

### Fixed

- Nothing.

## 1.1.2 - 2017-02-09

### Added

- [#4](https://github.com/php-fig/http-message-util/pull/4) adds the constant
  `StatusCodeInterface::STATUS_MISDIRECTED_REQUEST` (421).

### Deprecated

- Nothing.

### Removed

- Nothing.

### Fixed

- Nothing.

## 1.1.1 - 2017-02-06

### Added

- [#3](https://github.com/php-fig/http-message-util/pull/3) adds the constant
  `StatusCodeInterface::STATUS_IM_A_TEAPOT` (418).

### Deprecated

- Nothing.

### Removed

- Nothing.

### Fixed

- Nothing.

## 1.1.0 - 2016-09-19

### Added

- [#1](https://github.com/php-fig/http-message-util/pull/1) adds
  `Fig\Http\Message\StatusCodeInterface`, with constants named after common
  status reason phrases, with values indicating the status codes themselves.

### Deprecated

- Nothing.

### Removed

- Nothing.

### Fixed

- Nothing.

## 1.0.0 - 2017-08-05

### Added

- Adds `Fig\Http\Message\RequestMethodInterface`, with constants covering the
  most common HTTP request methods as specified by the IETF.

### Deprecated

- Nothing.

### Removed

- Nothing.

### Fixed

- Nothing.
