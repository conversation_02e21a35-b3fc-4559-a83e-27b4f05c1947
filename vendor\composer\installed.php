<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.1.3.x-dev',
        'version' => '1.1.3.9999999-dev',
        'reference' => 'e3e2abe68743716c5dc3c1569d2292d44b971fef',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.1.3.x-dev',
            'version' => '1.1.3.9999999-dev',
            'reference' => 'e3e2abe68743716c5dc3c1569d2292d44b971fef',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'beste/clock' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '7004b55fcd54737b539886244b3a3b2188181974',
            'type' => 'library',
            'install_path' => __DIR__ . '/../beste/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'beste/in-memory-cache' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'reference' => 'f8299adc8abdaf7d309e8b28e53b4307ea49ebc7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../beste/in-memory-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'beste/json' => array(
            'pretty_version' => '1.5.1',
            'version' => '1.5.1.0',
            'reference' => '0e9a0dc74fa6d1bb4f9883ef64fa9f36b7b6b934',
            'type' => 'library',
            'install_path' => __DIR__ . '/../beste/json',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.3',
            'version' => '0.12.3.0',
            'reference' => '866551da34e9a618e64a819ee1e01c20d8a588ba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.0',
            'version' => '6.11.0.0',
            'reference' => '8f718f4dfc9c5d5f0c994cdfd103921b43592712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.46.0',
            'version' => '1.46.0.0',
            'reference' => '7fafae99a41984cbfb92508174263cf7bf3049b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/cloud-core' => array(
            'pretty_version' => 'v1.62.1',
            'version' => '1.62.1.0',
            'reference' => '824a617d5c2b1b571673d1111856f5c2f064a0fd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/cloud-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/cloud-storage' => array(
            'pretty_version' => 'v1.47.0',
            'version' => '1.47.0.0',
            'reference' => 'c8f130fab6a48dad9c486e93a07cd26123d006bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/cloud-storage',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/common-protos' => array(
            'pretty_version' => '4.11.0',
            'version' => '4.11.0.0',
            'reference' => '2554ed1f09aa20faae7b71b590e7063df97ff670',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/common-protos',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/gax' => array(
            'pretty_version' => 'v1.36.0',
            'version' => '1.36.0.0',
            'reference' => '140599cf5eae2432363ce6198e9fdff851625a7a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/gax',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/grpc-gcp' => array(
            'pretty_version' => 'v0.4.1',
            'version' => '0.4.1.0',
            'reference' => 'e585b7721bbe806ef45b5c52ae43dfc2bff89968',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/grpc-gcp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/longrunning' => array(
            'pretty_version' => '0.4.7',
            'version' => '0.4.7.0',
            'reference' => '624cabb874c10e5ddc9034c999f724894b70a3d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/longrunning',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/protobuf' => array(
            'pretty_version' => 'v4.30.1',
            'version' => '4.30.1.0',
            'reference' => 'f29ba8a30dfd940efb3a8a75dc44446539101f24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/protobuf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'grpc/grpc' => array(
            'pretty_version' => '1.57.0',
            'version' => '1.57.0.0',
            'reference' => 'b610c42022ed3a22f831439cb93802f2a4502fdf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../grpc/grpc',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'f9c436286ab2892c7db7be8c8da4ef61ccf7b455',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kreait/firebase-php' => array(
            'pretty_version' => '7.18.0',
            'version' => '7.18.0.0',
            'reference' => 'f43bc2509721ae2bff77f5232e5f56225440393b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kreait/firebase-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kreait/firebase-tokens' => array(
            'pretty_version' => '5.2.1',
            'version' => '5.2.1.0',
            'reference' => 'df6f9d153f3bbe671c3247576d2a45cbd0a79620',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kreait/firebase-tokens',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lcobucci/jwt' => array(
            'pretty_version' => '5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'a835af59b030d3f2967725697cf88300f579088e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.8.0',
            'version' => '2.8.0.0',
            'reference' => 'a2a865e05d5f420b50cc2f85bb78d565db12a6bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0 || 3.0',
            ),
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '344572933ad0181accbf4ba763e85a0306a8c5e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.6',
            'version' => '4.7.6.0',
            'reference' => '91039bc1faa45ba123c4328958e620d382ec7088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.6',
            ),
        ),
        'rize/uri-template' => array(
            'pretty_version' => '0.4.0',
            'version' => '0.4.0.0',
            'reference' => '56f374a9a42c7c3998f8b55b6b21b224de90c58b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rize/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
