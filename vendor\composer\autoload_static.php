<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInita9cfa8b5d1d5a587bb5e2355faa68ea6
{
    public static $files = array (
        '7b11c4dc42b3b3023073cb14e519683c' => __DIR__ . '/..' . '/ralouphie/getallheaders/src/getallheaders.php',
        '6e3fae29631ef280660b3cdad06f25a8' => __DIR__ . '/..' . '/symfony/deprecation-contracts/function.php',
        '37a3dc5111fe8f707ab4c132ef1dbc62' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/functions_include.php',
        'e39a8b23c42d4e1452234d762b03835a' => __DIR__ . '/..' . '/ramsey/uuid/src/functions.php',
        '1232758e40b1d31bb295b6907de02c63' => __DIR__ . '/..' . '/beste/clock/src/Clock.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        '06b8a576aaa70a8517a94b7ff6b37e5c' => __DIR__ . '/..' . '/beste/json/src/Json.php',
        'b067bc7112e384b61c701452d53a14a8' => __DIR__ . '/..' . '/mtdowling/jmespath.php/src/JmesPath.php',
    );

    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'Symfony\\Polyfill\\Mbstring\\' => 26,
        ),
        'R' => 
        array (
            'Rize\\' => 5,
            'Ramsey\\Uuid\\' => 12,
            'Ramsey\\Collection\\' => 18,
        ),
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Http\\Client\\' => 16,
            'Psr\\Clock\\' => 10,
            'Psr\\Cache\\' => 10,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
        ),
        'L' => 
        array (
            'Lcobucci\\JWT\\' => 13,
        ),
        'K' => 
        array (
            'Kreait\\Firebase\\JWT\\' => 20,
            'Kreait\\Firebase\\' => 16,
        ),
        'J' => 
        array (
            'JmesPath\\' => 9,
        ),
        'G' => 
        array (
            'GuzzleHttp\\Psr7\\' => 16,
            'GuzzleHttp\\Promise\\' => 19,
            'GuzzleHttp\\' => 11,
            'Grpc\\Gcp\\' => 9,
            'Grpc\\' => 5,
            'Google\\Type\\' => 12,
            'Google\\Rpc\\' => 11,
            'Google\\Protobuf\\' => 16,
            'Google\\LongRunning\\' => 19,
            'Google\\Iam\\' => 11,
            'Google\\Cloud\\Storage\\' => 21,
            'Google\\Cloud\\Core\\' => 18,
            'Google\\Cloud\\' => 13,
            'Google\\Auth\\' => 12,
            'Google\\Api\\' => 11,
            'Google\\ApiCore\\LongRunning\\' => 27,
            'Google\\ApiCore\\' => 15,
            'GPBMetadata\\Google\\Type\\' => 24,
            'GPBMetadata\\Google\\Rpc\\' => 23,
            'GPBMetadata\\Google\\Protobuf\\' => 28,
            'GPBMetadata\\Google\\Longrunning\\' => 31,
            'GPBMetadata\\Google\\Logging\\' => 27,
            'GPBMetadata\\Google\\Iam\\' => 23,
            'GPBMetadata\\Google\\Cloud\\' => 25,
            'GPBMetadata\\Google\\Api\\' => 23,
            'GPBMetadata\\ApiCore\\' => 20,
        ),
        'F' => 
        array (
            'Firebase\\JWT\\' => 13,
            'Fig\\Http\\Message\\' => 17,
        ),
        'B' => 
        array (
            'Brick\\Math\\' => 11,
            'Beste\\Clock\\' => 12,
            'Beste\\Cache\\' => 12,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Rize\\' => 
        array (
            0 => __DIR__ . '/..' . '/rize/uri-template/src/Rize',
        ),
        'Ramsey\\Uuid\\' => 
        array (
            0 => __DIR__ . '/..' . '/ramsey/uuid/src',
        ),
        'Ramsey\\Collection\\' => 
        array (
            0 => __DIR__ . '/..' . '/ramsey/collection/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-factory/src',
            1 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Http\\Client\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-client/src',
        ),
        'Psr\\Clock\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/clock/src',
        ),
        'Psr\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/cache/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'Lcobucci\\JWT\\' => 
        array (
            0 => __DIR__ . '/..' . '/lcobucci/jwt/src',
        ),
        'Kreait\\Firebase\\JWT\\' => 
        array (
            0 => __DIR__ . '/..' . '/kreait/firebase-tokens/src/JWT',
        ),
        'Kreait\\Firebase\\' => 
        array (
            0 => __DIR__ . '/..' . '/kreait/firebase-php/src/Firebase',
        ),
        'JmesPath\\' => 
        array (
            0 => __DIR__ . '/..' . '/mtdowling/jmespath.php/src',
        ),
        'GuzzleHttp\\Psr7\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/psr7/src',
        ),
        'GuzzleHttp\\Promise\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/promises/src',
        ),
        'GuzzleHttp\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/guzzle/src',
        ),
        'Grpc\\Gcp\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/grpc-gcp/src',
        ),
        'Grpc\\' => 
        array (
            0 => __DIR__ . '/..' . '/grpc/grpc/src/lib',
        ),
        'Google\\Type\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/src/Type',
        ),
        'Google\\Rpc\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/src/Rpc',
        ),
        'Google\\Protobuf\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/protobuf/src/Google/Protobuf',
        ),
        'Google\\LongRunning\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/longrunning/src/LongRunning',
        ),
        'Google\\Iam\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/src/Iam',
        ),
        'Google\\Cloud\\Storage\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/cloud-storage/src',
        ),
        'Google\\Cloud\\Core\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/cloud-core/src',
        ),
        'Google\\Cloud\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/src/Cloud',
        ),
        'Google\\Auth\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/auth/src',
        ),
        'Google\\Api\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/src/Api',
        ),
        'Google\\ApiCore\\LongRunning\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/longrunning/src/ApiCore/LongRunning',
        ),
        'Google\\ApiCore\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/gax/src',
        ),
        'GPBMetadata\\Google\\Type\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/metadata/Type',
        ),
        'GPBMetadata\\Google\\Rpc\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/metadata/Rpc',
        ),
        'GPBMetadata\\Google\\Protobuf\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/protobuf/src/GPBMetadata/Google/Protobuf',
        ),
        'GPBMetadata\\Google\\Longrunning\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/longrunning/metadata/Longrunning',
        ),
        'GPBMetadata\\Google\\Logging\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/metadata/Logging',
        ),
        'GPBMetadata\\Google\\Iam\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/metadata/Iam',
        ),
        'GPBMetadata\\Google\\Cloud\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/metadata/Cloud',
        ),
        'GPBMetadata\\Google\\Api\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/common-protos/metadata/Api',
        ),
        'GPBMetadata\\ApiCore\\' => 
        array (
            0 => __DIR__ . '/..' . '/google/gax/metadata/ApiCore',
        ),
        'Firebase\\JWT\\' => 
        array (
            0 => __DIR__ . '/..' . '/firebase/php-jwt/src',
        ),
        'Fig\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/fig/http-message-util/src',
        ),
        'Brick\\Math\\' => 
        array (
            0 => __DIR__ . '/..' . '/brick/math/src',
        ),
        'Beste\\Clock\\' => 
        array (
            0 => __DIR__ . '/..' . '/beste/clock/src/Clock',
        ),
        'Beste\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/beste/in-memory-cache/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'GPBMetadata\\GrpcGcp' => __DIR__ . '/..' . '/google/grpc-gcp/src/generated/GPBMetadata/GrpcGcp.php',
        'Grpc\\Gcp\\AffinityConfig' => __DIR__ . '/..' . '/google/grpc-gcp/src/generated/Grpc/Gcp/AffinityConfig.php',
        'Grpc\\Gcp\\AffinityConfig_Command' => __DIR__ . '/..' . '/google/grpc-gcp/src/generated/Grpc/Gcp/AffinityConfig_Command.php',
        'Grpc\\Gcp\\ApiConfig' => __DIR__ . '/..' . '/google/grpc-gcp/src/generated/Grpc/Gcp/ApiConfig.php',
        'Grpc\\Gcp\\ChannelPoolConfig' => __DIR__ . '/..' . '/google/grpc-gcp/src/generated/Grpc/Gcp/ChannelPoolConfig.php',
        'Grpc\\Gcp\\MethodConfig' => __DIR__ . '/..' . '/google/grpc-gcp/src/generated/Grpc/Gcp/MethodConfig.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInita9cfa8b5d1d5a587bb5e2355faa68ea6::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInita9cfa8b5d1d5a587bb5e2355faa68ea6::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInita9cfa8b5d1d5a587bb5e2355faa68ea6::$classMap;

        }, null, ClassLoader::class);
    }
}
